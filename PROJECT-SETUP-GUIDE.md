# 项目模板配置指南

本文档介绍如何基于 Template Admin 模板工程快速创建新项目。

## 📋 概述

Template Admin 是一个可配置的项目模板，支持通过配置文件和自动化脚本快速生成新项目。只需要修改配置文件中的参数，运行一键配置脚本，即可生成一个全新的、可直接投入开发的项目。

## 🛠️ 环境要求

### 系统依赖
- **jq** - JSON 处理工具
- **sed** - 文本替换工具
- **find** - 文件查找工具

### 安装依赖

**Ubuntu/Debian:**
```bash
sudo apt-get update
sudo apt-get install jq
```

**CentOS/RHEL:**
```bash
sudo yum install jq
```

**macOS:**
```bash
brew install jq
```

**Windows (WSL):**
```bash
sudo apt-get install jq
```

## 📝 配置文件说明

配置文件 `project-config.json` 包含了项目的所有可配置参数：

### 项目基本信息
```json
{
  "project": {
    "name": "your-project-name",           // 项目名称（小写，用连字符）
    "displayName": "Your Project Name",   // 显示名称
    "description": "项目描述",             // 项目描述
    "version": "1.0.0",                   // 版本号
    "author": "Your Team",                // 作者
    "email": "<EMAIL>"    // 联系邮箱
  }
}
```

### 后端配置
```json
{
  "backend": {
    "groupId": "com.yourcompany",         // Maven GroupId
    "artifactId": "your-project-backend", // Maven ArtifactId
    "packageName": "com.yourcompany",     // Java 包名
    "applicationName": "your-api",        // 应用名称
    "serverPort": 8080,                   // 服务端口
    "contextPath": "/"                    // 上下文路径
  }
}
```

### 前端配置
```json
{
  "frontend": {
    "name": "your-project-frontend",      // NPM 包名
    "title": "Your Project Admin",       // 页面标题
    "devPort": 3000,                     // 开发端口
    "apiBaseUrl": "http://localhost:8080/api"  // API 基础地址
  }
}
```

### 数据库配置
```json
{
  "database": {
    "name": "your_database",             // 数据库名
    "host": "localhost",                 // 数据库主机
    "port": 3306,                        // 数据库端口
    "username": "root",                  // 用户名
    "password": "your_password",         // 密码
    "charset": "utf8mb4",                // 字符集
    "collation": "utf8mb4_unicode_ci"    // 排序规则
  }
}
```

### 安全配置
```json
{
  "security": {
    "jwtSecret": "your-strong-jwt-secret-key",  // JWT 密钥
    "accessTokenExpiration": 3600000,           // 访问令牌过期时间(毫秒)
    "refreshTokenExpiration": 604800000,        // 刷新令牌过期时间(毫秒)
    "druidUsername": "admin",                   // Druid 监控用户名
    "druidPassword": "your-druid-password"      // Druid 监控密码
  }
}
```

### OAuth2 配置
```json
{
  "oauth2": {
    "google": {
      "clientId": "your-google-client-id",
      "clientSecret": "your-google-client-secret"
    },
    "github": {
      "clientId": "your-github-client-id",
      "clientSecret": "your-github-client-secret"
    }
  }
}
```

## 🚀 快速开始

### 1. 准备配置文件

复制 `project-config.json` 并根据你的项目需求修改配置：

```bash
cp project-config.json my-project-config.json
```

编辑 `my-project-config.json`，修改以下关键配置：

```json
{
  "project": {
    "name": "my-awesome-admin",
    "displayName": "My Awesome Admin",
    "description": "我的超棒管理系统"
  },
  "backend": {
    "groupId": "com.mycompany",
    "packageName": "com.mycompany"
  },
  "database": {
    "name": "my_awesome_db",
    "password": "my_secure_password"
  }
}
```

### 2. 运行配置脚本

```bash
# 给脚本执行权限
chmod +x setup-project.sh

# 运行配置脚本
./setup-project.sh my-project-config.json
```

### 3. 初始化数据库

```bash
# 创建数据库并导入初始数据
mysql -u root -p < backend/sql/init.sql
```

### 4. 启动项目

**启动后端:**
```bash
cd backend/web-api
mvn spring-boot:run
```

**启动前端:**
```bash
cd frontend
npm install
npm run dev
```

### 5. 访问应用

- **前端地址**: http://localhost:3000 (或你配置的端口)
- **后端API**: http://localhost:8080/api (或你配置的端口)
- **Druid监控**: http://localhost:8080/druid

## 📁 配置脚本会修改的文件

### 后端文件
- `backend/pom.xml` - Maven 父项目配置
- `backend/*/pom.xml` - 各子模块配置
- `backend/web-api/src/main/resources/application.yml` - 应用配置
- `backend/sql/init.sql` - 数据库初始化脚本
- 所有 Java 文件的包名和导入语句
- Java 包目录结构

### 前端文件
- `frontend/package.json` - NPM 包配置
- `frontend/package-lock.json` - NPM 锁定文件
- `frontend/index.html` - HTML 页面标题
- `frontend/.env.development` - 开发环境变量
- `frontend/.env.production` - 生产环境变量

### 文档文件
- `README.md` - 主项目文档
- `frontend/README.md` - 前端文档
- `backend/README.md` - 后端文档

## ⚠️ 注意事项

### 配置建议
1. **项目名称** - 使用小写字母和连字符，避免特殊字符
2. **包名** - 遵循 Java 包命名规范，使用公司域名倒序
3. **数据库名** - 使用下划线命名，避免特殊字符
4. **密码安全** - 使用强密码，避免使用默认密码
5. **端口冲突** - 确保配置的端口未被占用

### 安全提醒
1. **JWT 密钥** - 生产环境必须使用强密钥
2. **数据库密码** - 不要使用默认密码
3. **OAuth2 密钥** - 妥善保管 OAuth2 客户端密钥
4. **环境变量** - 敏感信息建议使用环境变量

### 备份建议
在运行配置脚本前，建议备份原始模板：
```bash
cp -r template-admin my-project-backup
```

## 🔧 高级配置

### 自定义配置脚本

如果需要额外的配置项，可以修改 `setup-project.sh` 脚本：

1. 在 `project-config.json` 中添加新的配置项
2. 在脚本中添加相应的处理逻辑
3. 测试配置脚本的正确性

### 批量项目创建

可以创建多个配置文件，批量生成多个项目：

```bash
# 创建多个配置文件
cp project-config.json project-a-config.json
cp project-config.json project-b-config.json

# 分别配置并生成项目
./setup-project.sh project-a-config.json
./setup-project.sh project-b-config.json
```

## 🤝 贡献指南

如果你发现配置脚本的问题或有改进建议：

1. 提交 Issue 描述问题
2. Fork 项目并创建功能分支
3. 提交 Pull Request

## 📞 支持

如果在使用过程中遇到问题：

- 📧 邮箱: <EMAIL>
- 💬 QQ群: 123456789
- 🐛 问题反馈: GitHub Issues

---

祝你使用愉快！🚀
