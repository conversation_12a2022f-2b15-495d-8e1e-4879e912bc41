# Template Admin - 企业级管理后台系统

<div align="center">

![License](https://img.shields.io/badge/license-MIT-blue.svg)
![Java](https://img.shields.io/badge/Java-1.8-orange.svg)
![Spring Boot](https://img.shields.io/badge/Spring%20Boot-2.7.18-green.svg)
![Vue](https://img.shields.io/badge/Vue-3.5.17-brightgreen.svg)
![MySQL](https://img.shields.io/badge/MySQL-8.0-blue.svg)

一个现代化的企业级管理后台系统，采用前后端分离架构，支持OAuth2认证、JWT令牌管理、用户权限控制等功能。

[在线演示](http://localhost:3000) | [API文档](http://localhost:8080/api) | [问题反馈](https://github.com/your-repo/issues)

</div>

## ✨ 特性

### 🎯 核心功能
- **用户管理** - 完整的用户CRUD操作，支持角色权限管理
- **认证系统** - JWT令牌认证 + OAuth2社交登录（Google、GitHub）
- **服务管理** - 微服务配置管理，支持版本控制和监控
- **内容管理** - 富文本编辑器，支持Markdown格式
- **AI提示词** - AI提示词模板管理，支持分类和标签
- **任务管理** - 项目任务跟踪，支持甘特图和进度管理
- **数据看板** - 实时数据统计和可视化图表

### 🛠️ 技术特性
- **响应式设计** - 支持PC端优化，深色/浅色主题切换
- **模块化架构** - 前后端完全分离，支持独立部署
- **安全防护** - CORS跨域、SQL注入防护、XSS防护
- **性能优化** - 数据库连接池、缓存机制、懒加载
- **开发友好** - 热重载、Mock数据、调试工具

### 🎯 模板工程特性
- **一键生成** - 通过配置文件快速生成新项目
- **智能配置** - 自动替换项目名称、包路径、数据库配置
- **配置验证** - 内置配置文件验证，确保配置正确性
- **文档同步** - 自动更新README和相关文档
- **最佳实践** - 遵循企业级开发规范和安全标准
- **Git安全检查** - 自动检测Git状态，防止污染模板工程

### ⚠️ 重要提醒

**使用模板工程前，请务必先断开与原始仓库的Git连接！**

```bash
# ✅ 正确做法：克隆到新目录
git clone <template-repo-url> <new-project-name>
cd <new-project-name>
rm -rf .git

# ❌ 错误做法：直接在模板目录中运行脚本
# 这会污染原始模板工程
```

所有配置脚本都内置了Git状态检查，如果检测到项目仍连接到Git仓库，会提示用户先断开连接。

## 🏗️ 技术架构

### 前端技术栈
- **框架**: Vue 3.5.17 + Vue Router 4.5.1
- **状态管理**: Pinia 3.0.3
- **UI框架**: Tailwind CSS 3.4.17
- **构建工具**: Vite 7.0.2
- **编辑器**: Vditor 3.11.1 (Markdown)
- **代码高亮**: Highlight.js 11.11.1

### 后端技术栈
- **语言**: Java 1.8
- **框架**: Spring Boot 2.7.18
- **安全**: Spring Security + OAuth2 Client
- **数据库**: MySQL 8.0 + MyBatis
- **连接池**: Druid 1.2.18
- **认证**: JWT (JJWT 0.11.5)
- **构建工具**: Maven 3.6+

### 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (Vue3)    │    │   后端 (Spring)  │    │   数据库 (MySQL) │
│                 │    │                 │    │                 │
│ ├─ 路由管理      │    │ ├─ 认证授权      │    │ ├─ 用户表        │
│ ├─ 状态管理      │◄──►│ ├─ 业务逻辑      │◄──►│ ├─ 服务表        │
│ ├─ 组件库        │    │ ├─ 数据访问      │    │ ├─ 内容表        │
│ └─ 工具函数      │    │ └─ 接口层        │    │ └─ 日志表        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 快速开始

### 方式一：作为模板工程使用（推荐）

Template Admin 是一个可配置的项目模板，支持一键生成新项目：

#### 环境要求
- **jq**: JSON处理工具
- **Java**: JDK 1.8+
- **Node.js**: 16.0+
- **MySQL**: 8.0+
- **Maven**: 3.6+

#### 快速体验
```bash
# 1. 克隆模板工程到新目录
git clone https://github.com/your-username/template-admin.git my-test-project
cd my-test-project

# 2. 断开与模板仓库的连接（重要！）
rm -rf .git

# 3. 运行快速演示
./create-project.sh demo
```

#### 创建新项目
```bash
# 1. 克隆模板工程到新项目目录
git clone https://github.com/your-username/template-admin.git my-awesome-project
cd my-awesome-project

# 2. 断开与模板仓库的连接（重要！）
rm -rf .git

# 3. 复制并编辑配置文件
cp template-tools/project-config.json my-project-config.json
# 编辑 my-project-config.json，修改项目名称、包名等

# 4. 验证配置文件
./create-project.sh validate my-project-config.json

# 5. 生成新项目
./create-project.sh create my-project-config.json

# 6. 初始化数据库
mysql -u root -p < backend/sql/init.sql

# 7. 启动项目
cd backend/web-api && mvn spring-boot:run  # 后端
cd frontend && npm install && npm run dev  # 前端

# 8. （可选）初始化新的Git仓库
git init
git add .
git commit -m "Initial commit from template"
git remote add origin <your-new-repository-url>
git push -u origin main
```

详细配置说明请参考：[项目配置指南](template-tools/PROJECT-SETUP-GUIDE.md)

### 方式二：直接使用模板

#### 1. 克隆项目
```bash
git clone https://github.com/your-username/template-admin.git
cd template-admin
```

#### 2. 数据库配置
```bash
# 1. 创建数据库
mysql -u root -p
CREATE DATABASE ai CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 2. 导入初始数据
mysql -u root -p ai < backend/sql/init.sql
```

#### 3. 后端启动
```bash
cd backend
mvn clean install
cd web-api
mvn spring-boot:run
```

#### 4. 前端启动
```bash
cd frontend
npm install
npm run dev
```

#### 5. 访问应用
- **前端地址**: http://localhost:3000
- **后端API**: http://localhost:8080/api
- **数据库监控**: http://localhost:8080/druid

#### 默认账号
- **用户名**: admin
- **密码**: 123456

## 📁 项目结构

```
template-admin/
├── frontend/                   # 前端项目
│   ├── src/
│   │   ├── components/         # 通用组件
│   │   ├── views/             # 页面组件
│   │   ├── stores/            # 状态管理
│   │   ├── router/            # 路由配置
│   │   ├── utils/             # 工具函数
│   │   └── mock/              # Mock数据
│   ├── public/                # 静态资源
│   └── package.json           # 依赖配置
├── backend/                   # 后端项目
│   ├── common/                # 公共模块
│   ├── dao/                   # 数据访问层
│   ├── service/               # 业务逻辑层
│   ├── web-api/               # Web接口层
│   ├── sql/                   # 数据库脚本
│   └── pom.xml                # Maven配置
├── template-tools/            # 模板工具集
│   ├── project-config.json    # 默认配置模板
│   ├── example-project-config.json # 示例配置
│   ├── setup-project.sh       # 项目生成脚本
│   ├── validate-config.sh     # 配置验证脚本
│   ├── quick-start.sh         # 快速演示脚本
│   ├── PROJECT-SETUP-GUIDE.md # 配置指南
│   └── TEMPLATE-FEATURES.md   # 功能说明
├── create-project.sh          # 项目创建入口脚本
├── setup-oauth2.sh            # OAuth2配置脚本
├── OAuth2-解决方案.md          # OAuth2配置指南
└── README.md                  # 项目说明
```

## 🔧 配置说明

### OAuth2配置（可选）
系统支持Google和GitHub OAuth2登录，配置步骤：

1. **运行配置助手**
```bash
./setup-oauth2.sh
```

2. **获取OAuth2凭据**
- Google: https://console.cloud.google.com/
- GitHub: https://github.com/settings/developers

3. **更新配置文件**
```yaml
# backend/web-api/src/main/resources/application.yml
spring:
  security:
    oauth2:
      client:
        registration:
          google:
            client-id: "your-google-client-id"
            client-secret: "your-google-client-secret"
          github:
            client-id: "your-github-client-id"
            client-secret: "your-github-client-secret"
```

### 数据库配置
```yaml
# backend/web-api/src/main/resources/application.yml
spring:
  datasource:
    url: ******************************
    username: root
    password: root
```

## 📖 功能模块

### 用户管理
- 用户列表查看和搜索
- 用户信息增删改查
- 角色权限分配
- 账号绑定管理

### 服务管理
- 微服务配置管理
- 服务状态监控
- 版本历史记录
- 配置参数管理

### 内容管理
- 富文本内容编辑
- Markdown格式支持
- 分类标签管理
- 内容发布流程

### AI提示词
- 提示词模板管理
- 分类和标签系统
- 使用统计分析
- 导入导出功能

### 任务管理
- 项目任务跟踪
- 甘特图可视化
- 进度状态管理
- 工作日志记录

## 🔗 相关链接

### 📖 项目文档
- [前端README](./frontend/README.md) - 前端项目详细说明
- [后端README](./backend/README.md) - 后端项目详细说明
- [OAuth2配置指南](./OAuth2-解决方案.md) - OAuth2详细配置步骤

### 🛠️ 模板工程
- [模板工具说明](./template-tools/README.md) - 模板工具集使用说明
- [项目配置指南](./template-tools/PROJECT-SETUP-GUIDE.md) - 详细的模板使用指南
- [模板功能说明](./template-tools/TEMPLATE-FEATURES.md) - 完整功能介绍
- [配置文件示例](./template-tools/example-project-config.json) - 示例项目配置

### 🌐 在线资源
- [API文档](http://localhost:8080/api) - 后端API接口文档

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 💬 支持

如果您在使用过程中遇到问题，可以通过以下方式获取帮助：

- 📧 邮箱: <EMAIL>
- 💬 QQ群: 123456789
- 🐛 问题反馈: [GitHub Issues](https://github.com/your-repo/issues)

---

<div align="center">
Made with ❤️ by Template Admin Team
</div>
