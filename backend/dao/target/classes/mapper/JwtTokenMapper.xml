<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.dao.mapper.JwtTokenMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.dao.entity.JwtToken">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="token_id" property="tokenId" jdbcType="VARCHAR"/>
        <result column="access_token" property="accessToken" jdbcType="LONGVARCHAR"/>
        <result column="refresh_token" property="refreshToken" jdbcType="LONGVARCHAR"/>
        <result column="token_type" property="tokenType" jdbcType="VARCHAR"/>
        <result column="expires_at" property="expiresAt" jdbcType="TIMESTAMP"/>
        <result column="refresh_expires_at" property="refreshExpiresAt" jdbcType="TIMESTAMP"/>
        <result column="device_info" property="deviceInfo" jdbcType="VARCHAR"/>
        <result column="ip_address" property="ipAddress" jdbcType="VARCHAR"/>
        <result column="user_agent" property="userAgent" jdbcType="VARCHAR"/>
        <result column="is_revoked" property="isRevoked" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, user_id, token_id, access_token, refresh_token, token_type, expires_at, 
        refresh_expires_at, device_info, ip_address, user_agent, is_revoked, create_time, update_time
    </sql>

    <!-- 根据ID查询令牌 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_jwt_token
        WHERE id = #{id}
    </select>

    <!-- 根据令牌ID查询令牌 -->
    <select id="selectByTokenId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_jwt_token
        WHERE token_id = #{tokenId}
    </select>

    <!-- 根据用户ID查询有效令牌列表 -->
    <select id="selectValidTokensByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_jwt_token
        WHERE user_id = #{userId} 
          AND is_revoked = 0 
          AND expires_at > NOW()
        ORDER BY create_time DESC
    </select>

    <!-- 根据用户ID查询所有令牌 -->
    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_jwt_token
        WHERE user_id = #{userId}
        ORDER BY create_time DESC
    </select>

    <!-- 插入令牌 -->
    <insert id="insert" parameterType="com.example.dao.entity.JwtToken" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO sys_jwt_token
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="tokenId != null">token_id,</if>
            <if test="accessToken != null">access_token,</if>
            <if test="refreshToken != null">refresh_token,</if>
            <if test="tokenType != null">token_type,</if>
            <if test="expiresAt != null">expires_at,</if>
            <if test="refreshExpiresAt != null">refresh_expires_at,</if>
            <if test="deviceInfo != null">device_info,</if>
            <if test="ipAddress != null">ip_address,</if>
            <if test="userAgent != null">user_agent,</if>
            <if test="isRevoked != null">is_revoked,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="tokenId != null">#{tokenId},</if>
            <if test="accessToken != null">#{accessToken},</if>
            <if test="refreshToken != null">#{refreshToken},</if>
            <if test="tokenType != null">#{tokenType},</if>
            <if test="expiresAt != null">#{expiresAt},</if>
            <if test="refreshExpiresAt != null">#{refreshExpiresAt},</if>
            <if test="deviceInfo != null">#{deviceInfo},</if>
            <if test="ipAddress != null">#{ipAddress},</if>
            <if test="userAgent != null">#{userAgent},</if>
            <if test="isRevoked != null">#{isRevoked},</if>
        </trim>
    </insert>

    <!-- 更新令牌 -->
    <update id="updateById" parameterType="com.example.dao.entity.JwtToken">
        UPDATE sys_jwt_token
        <set>
            <if test="accessToken != null">access_token = #{accessToken},</if>
            <if test="refreshToken != null">refresh_token = #{refreshToken},</if>
            <if test="expiresAt != null">expires_at = #{expiresAt},</if>
            <if test="refreshExpiresAt != null">refresh_expires_at = #{refreshExpiresAt},</if>
            <if test="deviceInfo != null">device_info = #{deviceInfo},</if>
            <if test="ipAddress != null">ip_address = #{ipAddress},</if>
            <if test="userAgent != null">user_agent = #{userAgent},</if>
            <if test="isRevoked != null">is_revoked = #{isRevoked},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 撤销令牌 -->
    <update id="revokeToken">
        UPDATE sys_jwt_token
        SET is_revoked = 1, update_time = NOW()
        WHERE token_id = #{tokenId}
    </update>

    <!-- 撤销用户的所有令牌 -->
    <update id="revokeAllTokensByUserId">
        UPDATE sys_jwt_token
        SET is_revoked = 1, update_time = NOW()
        WHERE user_id = #{userId} AND is_revoked = 0
    </update>

    <!-- 根据ID删除令牌 -->
    <delete id="deleteById">
        DELETE FROM sys_jwt_token WHERE id = #{id}
    </delete>

    <!-- 删除过期令牌 -->
    <delete id="deleteExpiredTokens">
        DELETE FROM sys_jwt_token 
        WHERE expires_at &lt; NOW() OR (refresh_expires_at IS NOT NULL AND refresh_expires_at &lt; NOW())
    </delete>

    <!-- 删除用户的所有令牌 -->
    <delete id="deleteByUserId">
        DELETE FROM sys_jwt_token WHERE user_id = #{userId}
    </delete>

    <!-- 检查令牌是否存在且有效 -->
    <select id="isTokenValid" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM sys_jwt_token
        WHERE token_id = #{tokenId} 
          AND is_revoked = 0 
          AND expires_at > NOW()
    </select>

    <!-- 更新令牌最后使用时间 -->
    <update id="updateLastUsedTime">
        UPDATE sys_jwt_token
        SET update_time = NOW()
        WHERE token_id = #{tokenId}
    </update>

</mapper>
