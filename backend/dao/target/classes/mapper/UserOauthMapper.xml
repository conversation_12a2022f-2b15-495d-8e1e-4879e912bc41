<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.dao.mapper.UserOauthMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.dao.entity.UserOauth">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="provider" property="provider" jdbcType="VARCHAR"/>
        <result column="provider_id" property="providerId" jdbcType="VARCHAR"/>
        <result column="provider_username" property="providerUsername" jdbcType="VARCHAR"/>
        <result column="provider_email" property="providerEmail" jdbcType="VARCHAR"/>
        <result column="provider_avatar" property="providerAvatar" jdbcType="VARCHAR"/>
        <result column="access_token" property="accessToken" jdbcType="LONGVARCHAR"/>
        <result column="refresh_token" property="refreshToken" jdbcType="LONGVARCHAR"/>
        <result column="expires_at" property="expiresAt" jdbcType="TIMESTAMP"/>
        <result column="raw_user_info" property="rawUserInfo" jdbcType="LONGVARCHAR"/>
        <result column="is_primary" property="isPrimary" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, user_id, provider, provider_id, provider_username, provider_email, provider_avatar,
        access_token, refresh_token, expires_at, raw_user_info, is_primary, create_time, update_time
    </sql>

    <!-- 根据ID查询OAuth绑定 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_user_oauth
        WHERE id = #{id}
    </select>

    <!-- 根据用户ID查询OAuth绑定列表 -->
    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_user_oauth
        WHERE user_id = #{userId}
        ORDER BY is_primary DESC, create_time DESC
    </select>

    <!-- 根据提供商和提供商ID查询OAuth绑定 -->
    <select id="selectByProviderAndProviderId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_user_oauth
        WHERE provider = #{provider} AND provider_id = #{providerId}
    </select>

    <!-- 根据用户ID和提供商查询OAuth绑定 -->
    <select id="selectByUserIdAndProvider" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_user_oauth
        WHERE user_id = #{userId} AND provider = #{provider}
    </select>

    <!-- 插入OAuth绑定 -->
    <insert id="insert" parameterType="com.example.dao.entity.UserOauth" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO sys_user_oauth
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="provider != null">provider,</if>
            <if test="providerId != null">provider_id,</if>
            <if test="providerUsername != null">provider_username,</if>
            <if test="providerEmail != null">provider_email,</if>
            <if test="providerAvatar != null">provider_avatar,</if>
            <if test="accessToken != null">access_token,</if>
            <if test="refreshToken != null">refresh_token,</if>
            <if test="expiresAt != null">expires_at,</if>
            <if test="rawUserInfo != null">raw_user_info,</if>
            <if test="isPrimary != null">is_primary,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="provider != null">#{provider},</if>
            <if test="providerId != null">#{providerId},</if>
            <if test="providerUsername != null">#{providerUsername},</if>
            <if test="providerEmail != null">#{providerEmail},</if>
            <if test="providerAvatar != null">#{providerAvatar},</if>
            <if test="accessToken != null">#{accessToken},</if>
            <if test="refreshToken != null">#{refreshToken},</if>
            <if test="expiresAt != null">#{expiresAt},</if>
            <if test="rawUserInfo != null">#{rawUserInfo},</if>
            <if test="isPrimary != null">#{isPrimary},</if>
        </trim>
    </insert>

    <!-- 更新OAuth绑定 -->
    <update id="updateById" parameterType="com.example.dao.entity.UserOauth">
        UPDATE sys_user_oauth
        <set>
            <if test="providerUsername != null">provider_username = #{providerUsername},</if>
            <if test="providerEmail != null">provider_email = #{providerEmail},</if>
            <if test="providerAvatar != null">provider_avatar = #{providerAvatar},</if>
            <if test="accessToken != null">access_token = #{accessToken},</if>
            <if test="refreshToken != null">refresh_token = #{refreshToken},</if>
            <if test="expiresAt != null">expires_at = #{expiresAt},</if>
            <if test="rawUserInfo != null">raw_user_info = #{rawUserInfo},</if>
            <if test="isPrimary != null">is_primary = #{isPrimary},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除OAuth绑定 -->
    <delete id="deleteById">
        DELETE FROM sys_user_oauth WHERE id = #{id}
    </delete>

    <!-- 根据用户ID删除所有OAuth绑定 -->
    <delete id="deleteByUserId">
        DELETE FROM sys_user_oauth WHERE user_id = #{userId}
    </delete>

    <!-- 根据用户ID和提供商删除OAuth绑定 -->
    <delete id="deleteByUserIdAndProvider">
        DELETE FROM sys_user_oauth WHERE user_id = #{userId} AND provider = #{provider}
    </delete>

    <!-- 更新访问令牌 -->
    <update id="updateAccessToken">
        UPDATE sys_user_oauth
        SET access_token = #{accessToken},
            refresh_token = #{refreshToken},
            expires_at = #{expiresAt},
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 设置主要登录方式 -->
    <update id="setPrimaryProvider">
        UPDATE sys_user_oauth
        SET is_primary = CASE WHEN provider = #{provider} THEN 1 ELSE 0 END,
            update_time = NOW()
        WHERE user_id = #{userId}
    </update>

    <!-- 取消用户的所有主要登录方式 -->
    <update id="clearPrimaryProvider">
        UPDATE sys_user_oauth
        SET is_primary = 0,
            update_time = NOW()
        WHERE user_id = #{userId}
    </update>

</mapper>
