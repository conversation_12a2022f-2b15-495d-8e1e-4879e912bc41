2025-07-09 09:04:20 [main] INFO  com.example.Application - Starting Application using Java 24.0.1 on ZBMAC-c46123232 with PID 72869 (/Users/<USER>/ai_code/template_admin/backend/web-api/target/classes started by liandahu in /Users/<USER>/ai_code/template_admin/backend/web-api)
2025-07-09 09:04:20 [main] DEBUG com.example.Application - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-09 09:04:20 [main] INFO  com.example.Application - No active profile set, falling back to 1 default profile: "default"
2025-07-09 09:04:22 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-09 09:04:22 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 09:04:22 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-09 09:04:22 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-09 09:04:22 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1179 ms
2025-07-09 09:04:22 [main] ERROR o.s.boot.web.embedded.tomcat.TomcatStarter - Error starting Tomcat context. Exception: org.springframework.beans.factory.BeanCreationException. Message: Error creating bean with name 'jwtAuthenticationFilter' defined in file [/Users/<USER>/ai_code/template_admin/backend/web-api/target/classes/com/example/security/JwtAuthenticationFilter.class]: Post-processing of merged bean definition failed; nested exception is java.lang.IllegalStateException: Failed to introspect Class [com.example.security.JwtAuthenticationFilter] from ClassLoader [jdk.internal.loader.ClassLoaders$AppClassLoader@4d76f3f8]
2025-07-09 09:04:22 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-09 09:04:22 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
2025-07-09 09:04:22 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-09 09:04:22 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:165)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:585)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
	at com.example.Application.main(Application.java:15)
Caused by: org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:142)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.<init>(TomcatWebServer.java:104)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getTomcatWebServer(TomcatServletWebServerFactory.java:481)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getWebServer(TomcatServletWebServerFactory.java:211)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:184)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:162)
	... 8 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'jwtAuthenticationFilter' defined in file [/Users/<USER>/ai_code/template_admin/backend/web-api/target/classes/com/example/security/JwtAuthenticationFilter.class]: Post-processing of merged bean definition failed; nested exception is java.lang.IllegalStateException: Failed to introspect Class [com.example.security.JwtAuthenticationFilter] from ClassLoader [jdk.internal.loader.ClassLoaders$AppClassLoader@4d76f3f8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:597)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:213)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:213)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAsRegistrationBean(ServletContextInitializerBeans.java:176)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAsRegistrationBean(ServletContextInitializerBeans.java:171)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addAdaptableBeans(ServletContextInitializerBeans.java:156)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:87)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:262)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:236)
	at org.springframework.boot.web.embedded.tomcat.TomcatStarter.onStartup(TomcatStarter.java:53)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:4904)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1332)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1322)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:328)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:148)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:866)
	at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:794)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1332)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1322)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:328)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:148)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:866)
	at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:248)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.StandardService.startInternal(StandardService.java:433)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:921)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:171)
	at org.apache.catalina.startup.Tomcat.start(Tomcat.java:489)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:123)
	... 13 common frames omitted
Caused by: java.lang.IllegalStateException: Failed to introspect Class [com.example.security.JwtAuthenticationFilter] from ClassLoader [jdk.internal.loader.ClassLoaders$AppClassLoader@4d76f3f8]
	at org.springframework.util.ReflectionUtils.getDeclaredFields(ReflectionUtils.java:735)
	at org.springframework.util.ReflectionUtils.doWithLocalFields(ReflectionUtils.java:667)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.buildResourceMetadata(CommonAnnotationBeanPostProcessor.java:377)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.findResourceMetadata(CommonAnnotationBeanPostProcessor.java:358)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessMergedBeanDefinition(CommonAnnotationBeanPostProcessor.java:306)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyMergedBeanDefinitionPostProcessors(AbstractAutowireCapableBeanFactory.java:1116)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	... 50 common frames omitted
Caused by: java.lang.NoClassDefFoundError: com/example/common/util/JwtUtils
	at java.base/java.lang.Class.getDeclaredFields0(Native Method)
	at java.base/java.lang.Class.privateGetDeclaredFields(Class.java:2930)
	at java.base/java.lang.Class.getDeclaredFields(Class.java:2249)
	at org.springframework.util.ReflectionUtils.getDeclaredFields(ReflectionUtils.java:730)
	... 56 common frames omitted
Caused by: java.lang.ClassNotFoundException: com.example.common.util.JwtUtils
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:580)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:490)
	... 60 common frames omitted
2025-07-09 09:05:23 [main] INFO  com.example.Application - Starting Application using Java 24.0.1 on ZBMAC-c46123232 with PID 73466 (/Users/<USER>/ai_code/template_admin/backend/web-api/target/classes started by liandahu in /Users/<USER>/ai_code/template_admin/backend/web-api)
2025-07-09 09:05:23 [main] DEBUG com.example.Application - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-09 09:05:23 [main] INFO  com.example.Application - No active profile set, falling back to 1 default profile: "default"
2025-07-09 09:05:25 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-09 09:05:25 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 09:05:25 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-09 09:05:25 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-09 09:05:25 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1113 ms
2025-07-09 09:05:25 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-09 09:05:26 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-09 09:05:26 [main] DEBUG com.example.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-09 09:05:26 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@56266bda, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@18bb1b88, org.springframework.security.web.context.SecurityContextPersistenceFilter@59d5c537, org.springframework.security.web.header.HeaderWriterFilter@23469199, org.springframework.web.filter.CorsFilter@51b51641, org.springframework.security.web.authentication.logout.LogoutFilter@75452aea, org.springframework.security.oauth2.client.web.OAuth2AuthorizationRequestRedirectFilter@4e2109fe, org.springframework.security.oauth2.client.web.OAuth2LoginAuthenticationFilter@18dac12f, com.example.security.JwtAuthenticationFilter@64bba0eb, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3355168, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2f07930a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@362a6aa5, org.springframework.security.web.session.SessionManagementFilter@5611bba, org.springframework.security.web.access.ExceptionTranslationFilter@8af1c49, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6076c66]
2025-07-09 09:05:27 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-09 09:05:27 [main] INFO  com.example.Application - Started Application in 3.599 seconds (JVM running for 4.027)
2025-07-09 09:05:57 [http-nio-8080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 09:05:57 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-09 09:05:57 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-09 09:06:39 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-09 09:06:39 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-09 09:06:48 [main] INFO  com.example.Application - Starting Application using Java 24.0.1 on ZBMAC-c46123232 with PID 73918 (/Users/<USER>/ai_code/template_admin/backend/web-api/target/classes started by liandahu in /Users/<USER>/ai_code/template_admin/backend/web-api)
2025-07-09 09:06:48 [main] DEBUG com.example.Application - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-09 09:06:48 [main] INFO  com.example.Application - The following 1 profile is active: "oauth2"
2025-07-09 09:06:50 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-09 09:06:50 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 09:06:50 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-09 09:06:50 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-09 09:06:50 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1131 ms
2025-07-09 09:06:50 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-09 09:06:51 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-09 09:06:51 [main] DEBUG com.example.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-09 09:06:51 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@38dbeb39, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@10f20d38, org.springframework.security.web.context.SecurityContextPersistenceFilter@5db9f51f, org.springframework.security.web.header.HeaderWriterFilter@32d0d7eb, org.springframework.web.filter.CorsFilter@538aa83f, org.springframework.security.web.authentication.logout.LogoutFilter@258a8584, org.springframework.security.oauth2.client.web.OAuth2AuthorizationRequestRedirectFilter@2ffb0d10, org.springframework.security.oauth2.client.web.OAuth2LoginAuthenticationFilter@3982206a, com.example.security.JwtAuthenticationFilter@72eed547, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6c421123, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@38e00b47, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@63e70bf9, org.springframework.security.web.session.SessionManagementFilter@7d12429, org.springframework.security.web.access.ExceptionTranslationFilter@782e6b40, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@67b8d45]
2025-07-09 09:06:51 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-09 09:06:51 [main] INFO  com.example.Application - Started Application in 3.439 seconds (JVM running for 3.865)
2025-07-09 09:06:57 [http-nio-8080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 09:06:57 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-09 09:06:57 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-09 09:08:57 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-09 09:08:57 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-09 09:14:58 [main] INFO  com.example.Application - Starting Application using Java 1.8.0_421 on ZBMAC-c46123232 with PID 75272 (/Users/<USER>/ai_code/template_admin/backend/web-api/target/classes started by liandahu in /Users/<USER>/ai_code/template_admin/backend/web-api)
2025-07-09 09:14:58 [main] DEBUG com.example.Application - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-09 09:14:58 [main] INFO  com.example.Application - The following 1 profile is active: "oauth2"
2025-07-09 09:14:59 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-09 09:14:59 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 09:14:59 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-09 09:14:59 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-09 09:14:59 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1206 ms
2025-07-09 09:14:59 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-09 09:15:00 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-09 09:15:00 [main] DEBUG com.example.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-09 09:15:00 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3e0a9b1d, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@52f9e8bb, org.springframework.security.web.context.SecurityContextPersistenceFilter@b01cb8d, org.springframework.security.web.header.HeaderWriterFilter@5abf6a99, org.springframework.web.filter.CorsFilter@2035d65b, org.springframework.security.web.authentication.logout.LogoutFilter@446b64b3, org.springframework.security.oauth2.client.web.OAuth2AuthorizationRequestRedirectFilter@66d3b881, org.springframework.security.oauth2.client.web.OAuth2LoginAuthenticationFilter@3a45afd4, com.example.security.JwtAuthenticationFilter@3313d477, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4e67cfe1, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@55bf35e5, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@240a2619, org.springframework.security.web.session.SessionManagementFilter@375084c9, org.springframework.security.web.access.ExceptionTranslationFilter@5cc1bf20, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1b31af02]
2025-07-09 09:15:00 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-09 09:15:00 [main] INFO  com.example.Application - Started Application in 2.986 seconds (JVM running for 3.286)
2025-07-09 09:15:18 [http-nio-8080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 09:15:18 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-09 09:15:18 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-09 09:21:49 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-09 09:21:49 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-09 09:22:03 [main] INFO  com.example.Application - Starting Application using Java 1.8.0_421 on ZBMAC-c46123232 with PID 76325 (/Users/<USER>/ai_code/template_admin/backend/web-api/target/classes started by liandahu in /Users/<USER>/ai_code/template_admin/backend/web-api)
2025-07-09 09:22:03 [main] DEBUG com.example.Application - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-09 09:22:03 [main] INFO  com.example.Application - The following 1 profile is active: "oauth2"
2025-07-09 09:22:04 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-09 09:22:04 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 09:22:04 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-09 09:22:04 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-09 09:22:04 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1044 ms
2025-07-09 09:22:04 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-09 09:22:05 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-09 09:22:05 [main] DEBUG com.example.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-09 09:22:05 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@72224107, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@394fb736, org.springframework.security.web.context.SecurityContextPersistenceFilter@67763ebe, org.springframework.security.web.header.HeaderWriterFilter@4bbf38b8, org.springframework.web.filter.CorsFilter@3070f3e6, org.springframework.security.web.authentication.logout.LogoutFilter@4df7d9ee, org.springframework.security.oauth2.client.web.OAuth2AuthorizationRequestRedirectFilter@2035d65b, org.springframework.security.oauth2.client.web.OAuth2LoginAuthenticationFilter@240a2619, com.example.security.JwtAuthenticationFilter@73f6e07, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6b16de91, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@71bb8b34, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3271ec2a, org.springframework.security.web.session.SessionManagementFilter@55bf35e5, org.springframework.security.web.access.ExceptionTranslationFilter@2a136383, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6c995c5d]
2025-07-09 09:22:05 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-09 09:22:05 [main] INFO  com.example.Application - Started Application in 2.841 seconds (JVM running for 3.115)
2025-07-09 09:49:11 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-09 09:49:11 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-09 09:49:26 [main] INFO  com.example.Application - Starting Application using Java 1.8.0_421 on ZBMAC-c46123232 with PID 82258 (/Users/<USER>/ai_code/template_admin/backend/web-api/target/classes started by liandahu in /Users/<USER>/ai_code/template_admin/backend/web-api)
2025-07-09 09:49:26 [main] DEBUG com.example.Application - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-09 09:49:26 [main] INFO  com.example.Application - The following 1 profile is active: "oauth2"
2025-07-09 09:49:27 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-09 09:49:27 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 09:49:27 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-09 09:49:27 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-09 09:49:27 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1070 ms
2025-07-09 09:49:27 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-09 09:49:27 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-09 09:49:28 [main] DEBUG com.example.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-09 09:49:28 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3e0a9b1d, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@52f9e8bb, org.springframework.security.web.context.SecurityContextPersistenceFilter@b01cb8d, org.springframework.security.web.header.HeaderWriterFilter@5abf6a99, org.springframework.web.filter.CorsFilter@2035d65b, org.springframework.security.web.authentication.logout.LogoutFilter@446b64b3, org.springframework.security.oauth2.client.web.OAuth2AuthorizationRequestRedirectFilter@66d3b881, org.springframework.security.oauth2.client.web.OAuth2LoginAuthenticationFilter@3a45afd4, com.example.security.JwtAuthenticationFilter@3313d477, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4e67cfe1, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@55bf35e5, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@240a2619, org.springframework.security.web.session.SessionManagementFilter@375084c9, org.springframework.security.web.access.ExceptionTranslationFilter@5cc1bf20, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1b31af02]
2025-07-09 09:49:28 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-09 09:49:28 [main] INFO  com.example.Application - Started Application in 2.883 seconds (JVM running for 3.169)
2025-07-09 10:19:01 [http-nio-8080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 10:19:01 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-09 10:19:01 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-07-09 10:19:38 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-09 10:19:38 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-09 10:19:52 [main] INFO  com.example.Application - Starting Application using Java 1.8.0_421 on ZBMAC-c46123232 with PID 91773 (/Users/<USER>/ai_code/template_admin/backend/web-api/target/classes started by liandahu in /Users/<USER>/ai_code/template_admin/backend/web-api)
2025-07-09 10:19:52 [main] DEBUG com.example.Application - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-09 10:19:52 [main] INFO  com.example.Application - The following 1 profile is active: "oauth2"
2025-07-09 10:19:53 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-09 10:19:53 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 10:19:53 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-09 10:19:53 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-09 10:19:53 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1027 ms
2025-07-09 10:19:53 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-09 10:19:54 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-09 10:19:54 [main] DEBUG com.example.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-09 10:19:54 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3a45afd4, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@762a10b6, org.springframework.security.web.context.SecurityContextPersistenceFilter@5abf6a99, org.springframework.security.web.header.HeaderWriterFilter@6bb4cc0e, org.springframework.web.filter.CorsFilter@74431b9c, org.springframework.security.web.authentication.logout.LogoutFilter@100bba26, org.springframework.security.oauth2.client.web.OAuth2AuthorizationRequestRedirectFilter@7272ee51, org.springframework.security.oauth2.client.web.OAuth2LoginAuthenticationFilter@1b409a79, com.example.security.JwtAuthenticationFilter@24a0c58b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2a136383, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@b01cb8d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@f2fb225, org.springframework.security.web.session.SessionManagementFilter@2e3f324e, org.springframework.security.web.access.ExceptionTranslationFilter@6537ac, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4a9b3956]
2025-07-09 10:19:55 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-09 10:19:55 [main] INFO  com.example.Application - Started Application in 2.751 seconds (JVM running for 3.034)
2025-07-09 10:20:01 [http-nio-8080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 10:20:01 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-09 10:20:01 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-09 11:27:30 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-09 11:27:30 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-09 12:38:10 [main] INFO  com.example.Application - Starting Application using Java 1.8.0_421 on ZBMAC-c46123232 with PID 23207 (/Users/<USER>/ai_code/template_admin/backend/web-api/target/classes started by liandahu in /Users/<USER>/ai_code/template_admin/backend/web-api)
2025-07-09 12:38:10 [main] DEBUG com.example.Application - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-09 12:38:10 [main] INFO  com.example.Application - The following 1 profile is active: "oauth2"
2025-07-09 12:38:11 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-09 12:38:11 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 12:38:11 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-09 12:38:11 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-09 12:38:11 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1056 ms
2025-07-09 12:38:11 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-09 12:38:12 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-09 12:38:12 [main] DEBUG com.example.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-09 12:38:12 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@1883871b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3e0a9b1d, org.springframework.security.web.context.SecurityContextPersistenceFilter@2a136383, org.springframework.security.web.header.HeaderWriterFilter@b56ec6c, org.springframework.web.filter.CorsFilter@52f9e8bb, org.springframework.security.web.authentication.logout.LogoutFilter@1ad1c363, org.springframework.security.oauth2.client.web.OAuth2AuthorizationRequestRedirectFilter@795f6681, org.springframework.security.oauth2.client.web.OAuth2LoginAuthenticationFilter@66d3b881, com.example.security.JwtAuthenticationFilter@3a11c0eb, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4bbf38b8, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4e67cfe1, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2035d65b, org.springframework.security.web.session.SessionManagementFilter@dc59ec2, org.springframework.security.web.access.ExceptionTranslationFilter@6bb4cc0e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@643ecfef]
2025-07-09 12:38:12 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-09 12:38:12 [main] INFO  com.example.Application - Started Application in 2.826 seconds (JVM running for 3.112)
2025-07-09 12:39:10 [http-nio-8080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 12:39:10 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-09 12:39:10 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-09 13:10:46 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-09 13:10:46 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-09 13:11:14 [main] INFO  com.example.Application - Starting Application using Java 1.8.0_421 on ZBMAC-c46123232 with PID 32243 (/Users/<USER>/ai_code/template_admin/backend/web-api/target/classes started by liandahu in /Users/<USER>/ai_code/template_admin/backend/web-api)
2025-07-09 13:11:14 [main] DEBUG com.example.Application - Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-09 13:11:14 [main] INFO  com.example.Application - The following 1 profile is active: "oauth2"
2025-07-09 13:11:15 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-09 13:11:15 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-09 13:11:15 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-07-09 13:11:16 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-09 13:11:16 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1085 ms
2025-07-09 13:11:16 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-09 13:11:16 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-09 13:11:16 [main] DEBUG com.example.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-09 13:11:17 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3e0a9b1d, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@52f9e8bb, org.springframework.security.web.context.SecurityContextPersistenceFilter@b01cb8d, org.springframework.security.web.header.HeaderWriterFilter@5abf6a99, org.springframework.web.filter.CorsFilter@2035d65b, org.springframework.security.web.authentication.logout.LogoutFilter@446b64b3, org.springframework.security.oauth2.client.web.OAuth2AuthorizationRequestRedirectFilter@66d3b881, org.springframework.security.oauth2.client.web.OAuth2LoginAuthenticationFilter@3a45afd4, com.example.security.JwtAuthenticationFilter@3313d477, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4e67cfe1, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@55bf35e5, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@240a2619, org.springframework.security.web.session.SessionManagementFilter@375084c9, org.springframework.security.web.access.ExceptionTranslationFilter@5cc1bf20, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1b31af02]
2025-07-09 13:11:17 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-09 13:11:17 [main] INFO  com.example.Application - Started Application in 2.864 seconds (JVM running for 3.179)
2025-07-09 13:11:27 [http-nio-8080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-09 13:11:27 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-09 13:11:27 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-09 14:29:00 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-09 14:29:00 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
