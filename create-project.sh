#!/bin/bash

# 项目创建入口脚本
# 从项目根目录调用模板工具

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "🚀 Template Admin 项目创建工具"
    echo "================================"
    echo ""
    echo "使用方法:"
    echo "  $0 [命令] [参数]"
    echo ""
    echo "命令:"
    echo "  demo                    运行快速演示"
    echo "  validate <config-file>  验证配置文件"
    echo "  create <config-file>    创建新项目"
    echo "  help                    显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 demo"
    echo "  $0 validate template-tools/example-project-config.json"
    echo "  $0 create my-project-config.json"
    echo ""
    echo "配置文件:"
    echo "  默认配置模板: template-tools/project-config.json"
    echo "  示例配置: template-tools/example-project-config.json"
    echo ""
    echo "详细文档:"
    echo "  template-tools/PROJECT-SETUP-GUIDE.md"
}

# 检查模板工具目录
check_template_tools() {
    if [[ ! -d "template-tools" ]]; then
        log_error "template-tools 目录不存在"
        log_error "请确保在项目根目录下运行此脚本"
        exit 1
    fi
    
    local required_files=(
        "template-tools/setup-project.sh"
        "template-tools/validate-config.sh"
        "template-tools/quick-start.sh"
        "template-tools/project-config.json"
    )
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            log_error "缺少必需文件: $file"
            exit 1
        fi
    done
}

# 运行快速演示
run_demo() {
    log_info "启动快速演示..."
    cd template-tools
    ./quick-start.sh
}

# 验证配置文件
validate_config() {
    local config_file="$1"
    
    if [[ -z "$config_file" ]]; then
        log_error "请指定配置文件"
        echo "使用方法: $0 validate <config-file>"
        exit 1
    fi
    
    # 如果配置文件不在template-tools目录下，检查是否存在
    if [[ ! -f "$config_file" && ! -f "template-tools/$config_file" ]]; then
        log_error "配置文件不存在: $config_file"
        exit 1
    fi
    
    # 如果配置文件在当前目录，移动到template-tools目录进行验证
    if [[ -f "$config_file" ]]; then
        cd template-tools
        ./validate-config.sh "../$config_file"
    else
        cd template-tools
        ./validate-config.sh "$config_file"
    fi
}

# 创建新项目
create_project() {
    local config_file="$1"
    
    if [[ -z "$config_file" ]]; then
        log_error "请指定配置文件"
        echo "使用方法: $0 create <config-file>"
        exit 1
    fi
    
    # 如果配置文件不在template-tools目录下，检查是否存在
    if [[ ! -f "$config_file" && ! -f "template-tools/$config_file" ]]; then
        log_error "配置文件不存在: $config_file"
        exit 1
    fi
    
    # 如果配置文件在当前目录，移动到template-tools目录进行处理
    if [[ -f "$config_file" ]]; then
        cd template-tools
        ./setup-project.sh "../$config_file"
    else
        cd template-tools
        ./setup-project.sh "$config_file"
    fi
}

# 主函数
main() {
    # 检查模板工具
    check_template_tools
    
    # 解析命令
    case "${1:-help}" in
        "demo")
            run_demo
            ;;
        "validate")
            validate_config "$2"
            ;;
        "create")
            create_project "$2"
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
