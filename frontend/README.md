# Template Admin Frontend

<div align="center">

![Vue](https://img.shields.io/badge/Vue-3.5.17-brightgreen.svg)
![Vite](https://img.shields.io/badge/Vite-7.0.2-646CFF.svg)
![TailwindCSS](https://img.shields.io/badge/TailwindCSS-3.4.17-38B2AC.svg)
![Pinia](https://img.shields.io/badge/Pinia-3.0.3-yellow.svg)

现代化的企业级管理后台前端系统，基于 Vue 3 + Vite + TailwindCSS 构建

[在线演示](http://localhost:3000) | [后端项目](../backend/README.md) | [项目文档](../README.md)

</div>

## ✨ 特性

### 🎯 核心功能
- **用户管理** - 用户列表、权限管理、账号绑定
- **服务管理** - 微服务配置、状态监控、版本控制
- **内容管理** - 富文本编辑、Markdown支持、分类标签
- **AI提示词** - 提示词模板、分类管理、使用统计
- **任务管理** - 项目跟踪、甘特图、进度管理
- **数据看板** - 实时统计、可视化图表

### 🛠️ 技术特性
- **现代化架构** - Vue 3 Composition API + Vite
- **响应式设计** - 支持PC端优化，深色/浅色主题
- **组件化开发** - 可复用组件库，统一设计规范
- **状态管理** - Pinia状态管理，模块化设计
- **路由系统** - Vue Router 4，支持懒加载和权限控制
- **开发体验** - 热重载、Mock数据、调试工具

## 🛠️ 技术栈

### 核心框架
- **Vue 3.5.17** - 渐进式JavaScript框架，Composition API
- **Vite 7.0.2** - 下一代前端构建工具，快速热重载
- **Vue Router 4.5.1** - 官方路由管理器，支持懒加载
- **Pinia 3.0.3** - 现代化状态管理库

### UI框架
- **TailwindCSS 3.4.17** - 实用优先的CSS框架
- **响应式设计** - PC端优化，支持深色/浅色主题
- **组件库** - 自建可复用组件系统

### 功能增强
- **Vditor 3.11.1** - 所见即所得Markdown编辑器
- **Marked 16.0.0** - Markdown解析和渲染
- **Highlight.js 11.11.1** - 代码语法高亮

### 开发工具
- **PostCSS + Autoprefixer** - CSS后处理和兼容性
- **Mock数据** - 完整的前端Mock系统
- **调试工具** - 开发调试辅助功能

## 🚀 快速开始

### 环境要求
- **Node.js**: 16.0+
- **npm**: 7.0+ 或 **yarn**: 1.22+

### 安装和运行

```bash
# 1. 进入前端目录
cd frontend

# 2. 安装依赖
npm install

# 3. 启动开发服务器
npm run dev

# 4. 构建生产版本
npm run build

# 5. 预览构建结果
npm run preview
```

### 访问地址
- **开发环境**: http://localhost:3000
- **API代理**: 自动代理到后端 http://localhost:8080

### 默认账号
- **用户名**: admin
- **密码**: 123456

## 📁 项目结构

```
frontend/
├── public/                     # 静态资源
│   ├── placeholder-logo.png    # 占位符图片
│   └── placeholder-user.jpg    # 用户头像占位符
├── src/                        # 源代码
│   ├── components/             # 组件库
│   │   ├── details/           # 详情页组件
│   │   │   ├── ServiceDetail.vue
│   │   │   ├── PromptDetail.vue
│   │   │   └── ContentDetail.vue
│   │   ├── layout/            # 布局组件
│   │   │   ├── AppLayout.vue
│   │   │   ├── AppSidebar.vue
│   │   │   └── Header.vue
│   │   ├── ui/                # UI基础组件
│   │   │   ├── DetailModal.vue
│   │   │   ├── DetailPage.vue
│   │   │   ├── TabNavigation.vue
│   │   │   └── MarkdownEditor.vue
│   │   ├── ActionButton.vue   # 操作按钮
│   │   ├── PageHeader.vue     # 页面头部
│   │   ├── Pagination.vue     # 分页组件
│   │   └── SearchArea.vue     # 搜索区域
│   ├── config/                # 配置文件
│   │   └── detailTemplates.js # 详情页模板配置
│   ├── mock/                  # Mock数据
│   │   ├── data/             # 模拟数据
│   │   └── utils.js          # Mock工具函数
│   ├── router/               # 路由配置
│   │   └── index.js          # 路由定义
│   ├── stores/               # 状态管理
│   │   ├── theme.js          # 主题状态
│   │   └── user.js           # 用户状态
│   ├── utils/                # 工具函数
│   │   ├── accessibility.js   # 无障碍功能
│   │   ├── autoSave.js       # 自动保存
│   │   ├── debugHelper.js    # 调试辅助
│   │   ├── markdown.js       # Markdown处理
│   │   └── performance.js    # 性能监控
│   ├── views/                # 页面组件
│   │   ├── Dashboard.vue     # 数据看板
│   │   ├── Users.vue         # 用户管理
│   │   ├── Services.vue      # 服务管理
│   │   ├── Prompts.vue       # AI提示词
│   │   ├── Content.vue       # 内容管理
│   │   ├── Tasks.vue         # 任务管理
│   │   ├── Login.vue         # 登录页面
│   │   ├── ServiceDetailPage.vue    # 服务详情页
│   │   ├── PromptDetailPage.vue     # 提示词详情页
│   │   └── ContentDetailPage.vue    # 内容详情页
│   ├── App.vue               # 根组件
│   ├── main.js               # 应用入口
│   └── style.css             # 全局样式
├── index.html                # HTML模板
├── package.json              # 项目配置
├── vite.config.js            # Vite配置
├── tailwind.config.js        # TailwindCSS配置
├── postcss.config.js         # PostCSS配置
└── README.md                 # 项目文档
```

## 🔧 核心功能

### 页面管理
- **数据看板** - 实时统计数据和可视化图表
- **用户管理** - 用户CRUD操作、权限管理、账号绑定
- **服务管理** - 微服务配置、状态监控、版本历史
- **内容管理** - 富文本编辑、Markdown支持、分类标签
- **AI提示词** - 提示词模板管理、分类系统、使用统计
- **任务管理** - 项目跟踪、甘特图、进度管理

### 组件系统
- **布局组件** - 响应式布局、侧边栏、头部导航
- **表单组件** - 输入框、选择器、文件上传
- **数据组件** - 表格、分页、搜索、筛选
- **反馈组件** - 弹窗、提示、确认对话框
- **编辑器** - Markdown编辑器、富文本编辑器

### 主题系统
- **深色/浅色模式** - 完整的主题切换支持
- **响应式设计** - PC端优化，支持移动端适配
- **自定义主题** - 可配置的颜色和样式变量

### 状态管理
- **用户状态** - 登录状态、权限信息、个人设置
- **主题状态** - 主题模式、界面配置
- **应用状态** - 全局配置、缓存管理

## 🛠️ 开发指南

### 环境配置

```bash
# 开发环境变量 (.env.development)
VITE_API_BASE_URL=http://localhost:8080/api
VITE_APP_TITLE=Template Admin (Dev)

# 生产环境变量 (.env.production)
VITE_API_BASE_URL=https://api.yourdomain.com
VITE_APP_TITLE=Template Admin
```

### 添加新页面

1. **创建页面组件**
```vue
<!-- src/views/NewPage.vue -->
<template>
  <div class="space-y-6">
    <PageHeader title="新页面" description="页面描述" />
    <!-- 页面内容 -->
  </div>
</template>

<script setup>
import PageHeader from '@/components/PageHeader.vue'
</script>
```

2. **添加路由配置**
```javascript
// src/router/index.js
{
  path: "/new-page",
  name: "new-page",
  component: () => import("@/views/NewPage.vue"),
  meta: { title: "新页面", requiresAuth: true }
}
```

3. **更新侧边栏菜单**
```vue
<!-- src/components/layout/AppSidebar.vue -->
<!-- 在菜单配置中添加新的导航项 -->
```

### 使用组件

```vue
<template>
  <!-- 页面头部 -->
  <PageHeader title="页面标题" description="页面描述">
    <template #actions>
      <ActionButton variant="primary" @click="handleAction">
        操作按钮
      </ActionButton>
    </template>
  </PageHeader>

  <!-- 搜索区域 -->
  <SearchArea
    v-model="searchForm"
    :filters="searchFilters"
    @search="handleSearch"
    @reset="handleReset"
  />

  <!-- 分页组件 -->
  <Pagination
    :current-page="pagination.currentPage"
    :total="pagination.total"
    :page-size="pagination.pageSize"
    @page-change="handlePageChange"
  />
</template>
```

### 状态管理

```javascript
// src/stores/example.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useExampleStore = defineStore('example', () => {
  const data = ref([])
  const loading = ref(false)

  const filteredData = computed(() => {
    return data.value.filter(item => item.active)
  })

  const fetchData = async () => {
    loading.value = true
    try {
      // API调用
      const response = await fetch('/api/data')
      data.value = await response.json()
    } finally {
      loading.value = false
    }
  }

  return {
    data,
    loading,
    filteredData,
    fetchData
  }
})
```

## 🚀 部署指南

### 构建生产版本

```bash
# 构建项目
npm run build

# 预览构建结果
npm run preview
```

### Nginx配置

```nginx
server {
    listen 80;
    server_name yourdomain.com;
    root /var/www/html;
    index index.html;

    # 处理Vue Router的history模式
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # API代理
    location /api {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### Docker部署

```dockerfile
# Dockerfile
FROM node:18-alpine as build-stage

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine as production-stage
COPY --from=build-stage /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## ❓ 常见问题

### Q: 如何修改API接口地址？
A: 在环境变量文件中配置：
```bash
# .env.development
VITE_API_BASE_URL=http://localhost:8080/api
```

### Q: 如何添加新的主题色？
A: 在 `tailwind.config.js` 中扩展颜色配置：
```javascript
theme: {
  extend: {
    colors: {
      brand: {
        50: '#f0f9ff',
        500: '#0ea5e9',
        900: '#0c4a6e',
      }
    }
  }
}
```

### Q: 如何自定义侧边栏菜单？
A: 编辑 `src/components/layout/AppSidebar.vue` 文件中的菜单配置。

### Q: 如何集成第三方组件库？
A: 安装组件库并在 `main.js` 中全局注册：
```javascript
import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import App from './App.vue'

const app = createApp(App)
app.use(ElementPlus)
app.mount('#app')
```

## 🔗 相关链接

- [项目主页](../README.md) - 项目整体介绍和快速开始
- [后端文档](../backend/README.md) - 后端API和服务说明
- [OAuth2配置](../OAuth2-解决方案.md) - OAuth2登录配置指南

## 🤝 贡献指南

### 开发流程
1. Fork 项目
2. 创建功能分支: `git checkout -b feature/your-feature`
3. 提交更改: `git commit -am 'Add some feature'`
4. 推送分支: `git push origin feature/your-feature`
5. 创建 Pull Request

### 代码规范
- 使用 Vue 3 Composition API
- 组件命名使用 PascalCase
- 文件命名使用 kebab-case
- 遵循 ESLint 和 Prettier 规范

### 提交规范
```
feat: 添加新功能
fix: 修复bug
docs: 更新文档
style: 代码格式调整
refactor: 代码重构
test: 添加测试
chore: 构建过程或辅助工具的变动
```

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](../LICENSE) 文件了解详情

## 💬 支持

如果您在使用过程中遇到问题，可以通过以下方式获取帮助：

- 📧 邮箱: <EMAIL>
- 💬 QQ群: 123456789
- 🐛 问题反馈: [GitHub Issues](https://github.com/your-repo/issues)

---

<div align="center">
Made with ❤️ by Template Admin Team
</div>



