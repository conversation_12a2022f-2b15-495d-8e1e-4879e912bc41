# OAuth2 登录错误解决方案

## 🚨 问题描述
遇到 "错误 401：invalid_client" 是因为OAuth2客户端配置不正确。

## 🔧 解决方案

### 方案1：快速测试（推荐）
暂时使用传统用户名密码登录，OAuth2按钮已显示配置提示。

**测试账号：**
- 用户名：`admin`
- 密码：`123456`

### 方案2：配置真实OAuth2（完整功能）

#### 🔵 Google OAuth2 配置

1. **访问 Google Cloud Console**
   ```
   https://console.cloud.google.com/
   ```

2. **创建项目**
   - 点击项目选择器
   - 创建新项目（如：Template Admin）

3. **启用API**
   - 导航到 "API和服务" → "库"
   - 搜索并启用 "Google+ API"

4. **创建OAuth2凭据**
   - 导航到 "API和服务" → "凭据"
   - 点击 "创建凭据" → "OAuth 2.0 客户端ID"
   - 应用类型：Web应用
   - 名称：Template Admin
   - 授权的重定向URI：
     ```
     http://localhost:8080/login/oauth2/code/google
     ```

5. **获取凭据**
   - 复制客户端ID和客户端密钥

#### 🔵 GitHub OAuth2 配置

1. **访问 GitHub Developer Settings**
   ```
   https://github.com/settings/developers
   ```

2. **创建OAuth App**
   - 点击 "New OAuth App"
   - Application name: Template Admin
   - Homepage URL: http://localhost:3000
   - Authorization callback URL:
     ```
     http://localhost:8080/login/oauth2/code/github
     ```

3. **获取凭据**
   - 复制客户端ID和客户端密钥

#### 🔧 配置后端

1. **编辑配置文件**
   ```bash
   # 编辑 backend/web-api/src/main/resources/application.yml
   ```

2. **替换OAuth2配置**
   ```yaml
   spring:
     security:
       oauth2:
         client:
           registration:
             google:
               client-id: "你的Google客户端ID"
               client-secret: "你的Google客户端密钥"
               scope:
                 - openid
                 - profile
                 - email
               redirect-uri: "{baseUrl}/login/oauth2/code/{registrationId}"
             github:
               client-id: "你的GitHub客户端ID"
               client-secret: "你的GitHub客户端密钥"
               scope:
                 - user:email
                 - read:user
               redirect-uri: "{baseUrl}/login/oauth2/code/{registrationId}"
   ```

3. **重启后端服务**
   ```bash
   cd backend/web-api
   mvn spring-boot:run
   ```

## 🎯 验证配置

1. **检查后端日志**
   - 确保没有OAuth2相关错误

2. **测试OAuth2登录**
   - 访问 http://localhost:3000/login
   - 点击Google或GitHub登录按钮
   - 完成授权流程

## 📋 当前状态

✅ **已完成：**
- JWT认证系统
- 用户管理功能
- 账号绑定页面
- OAuth2框架集成
- 错误提示和配置指南

⚠️ **需要配置：**
- Google OAuth2客户端凭据
- GitHub OAuth2客户端凭据

## 🚀 快速启动

如果暂时不需要OAuth2登录，可以直接使用：

1. **访问登录页面**
   ```
   http://localhost:3000/login
   ```

2. **使用测试账号**
   - 用户名：admin
   - 密码：123456

3. **体验功能**
   - 用户管理
   - 账号绑定页面
   - JWT令牌管理

## 🔗 相关文件

- `backend/oauth2-setup.md` - 详细配置指南
- `backend/web-api/src/main/resources/application-oauth2.yml` - 配置模板
- `setup-oauth2.sh` - 配置助手脚本

## 💡 提示

OAuth2配置是可选的，系统的核心功能（用户管理、JWT认证等）已经完全可用。您可以：

1. 先体验现有功能
2. 需要时再配置OAuth2
3. 或者只配置其中一个（Google或GitHub）
