#!/bin/bash

echo "🔧 OAuth2 配置助手"
echo "=================="
echo ""

echo "此脚本将帮助您配置Google和GitHub OAuth2登录功能。"
echo ""

# 检查是否已有配置文件
if [ -f "backend/web-api/src/main/resources/application-oauth2.yml" ]; then
    echo "✅ 发现OAuth2配置模板文件"
else
    echo "❌ 未找到OAuth2配置模板文件"
    exit 1
fi

echo ""
echo "📋 配置步骤："
echo ""

echo "1️⃣  Google OAuth2 配置："
echo "   - 访问: https://console.cloud.google.com/"
echo "   - 创建项目并启用 Google+ API"
echo "   - 创建 OAuth 2.0 客户端ID"
echo "   - 重定向URI: http://localhost:8080/login/oauth2/code/google"
echo ""

echo "2️⃣  GitHub OAuth2 配置："
echo "   - 访问: https://github.com/settings/developers"
echo "   - 创建新的 OAuth App"
echo "   - 回调URL: http://localhost:8080/login/oauth2/code/github"
echo ""

echo "3️⃣  配置文件更新："
echo "   - 编辑 backend/web-api/src/main/resources/application-oauth2.yml"
echo "   - 将获取的客户端ID和密钥填入配置文件"
echo ""

echo "4️⃣  启动应用："
echo "   cd backend/web-api"
echo "   mvn spring-boot:run -Dspring-boot.run.profiles=oauth2"
echo ""

read -p "是否现在打开配置文件进行编辑？(y/n): " -n 1 -r
echo ""

if [[ $REPLY =~ ^[Yy]$ ]]; then
    if command -v code &> /dev/null; then
        echo "🚀 使用 VS Code 打开配置文件..."
        code backend/web-api/src/main/resources/application-oauth2.yml
    elif command -v nano &> /dev/null; then
        echo "🚀 使用 nano 打开配置文件..."
        nano backend/web-api/src/main/resources/application-oauth2.yml
    elif command -v vim &> /dev/null; then
        echo "🚀 使用 vim 打开配置文件..."
        vim backend/web-api/src/main/resources/application-oauth2.yml
    else
        echo "📝 请手动编辑文件: backend/web-api/src/main/resources/application-oauth2.yml"
    fi
fi

echo ""
echo "✨ 配置完成后，使用以下命令启动应用："
echo "   cd backend/web-api && mvn spring-boot:run -Dspring-boot.run.profiles=oauth2"
echo ""
echo "🎉 然后就可以在登录页面使用OAuth2登录了！"
