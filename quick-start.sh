#!/bin/bash

# 快速开始脚本
# 演示如何使用模板工程创建新项目

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    echo ""
    echo "🚀 Template Admin 快速开始"
    echo "=========================="
    echo ""
    echo "这个脚本将演示如何使用 Template Admin 模板工程创建新项目。"
    echo ""
    echo "演示流程："
    echo "1. 验证示例配置文件"
    echo "2. 创建项目副本"
    echo "3. 应用配置"
    echo "4. 验证结果"
    echo ""
}

# 检查依赖
check_dependencies() {
    log_step "检查系统依赖..."
    
    local missing_deps=()
    
    # 检查必需的命令
    for cmd in jq sed find cp mv; do
        if ! command -v "$cmd" &> /dev/null; then
            missing_deps+=("$cmd")
        fi
    done
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        log_error "缺少以下依赖: ${missing_deps[*]}"
        echo ""
        echo "安装方法："
        echo "Ubuntu/Debian: sudo apt-get install jq"
        echo "CentOS/RHEL: sudo yum install jq"
        echo "macOS: brew install jq"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 验证配置文件
validate_config() {
    log_step "验证示例配置文件..."
    
    if [[ ! -f "validate-config.sh" ]]; then
        log_error "验证脚本不存在: validate-config.sh"
        exit 1
    fi
    
    if [[ ! -f "example-project-config.json" ]]; then
        log_error "示例配置文件不存在: example-project-config.json"
        exit 1
    fi
    
    # 运行验证脚本
    if ./validate-config.sh example-project-config.json; then
        log_success "配置文件验证通过"
    else
        log_error "配置文件验证失败"
        exit 1
    fi
}

# 创建项目副本
create_project_copy() {
    log_step "创建项目副本..."
    
    local demo_dir="demo-ecommerce-admin"
    
    # 删除已存在的演示目录
    if [[ -d "$demo_dir" ]]; then
        log_warning "删除已存在的演示目录: $demo_dir"
        rm -rf "$demo_dir"
    fi
    
    # 创建项目副本
    log_info "复制模板工程到: $demo_dir"
    cp -r . "$demo_dir"
    
    # 进入演示目录
    cd "$demo_dir"
    
    log_success "项目副本创建完成"
}

# 应用配置
apply_configuration() {
    log_step "应用项目配置..."
    
    if [[ ! -f "setup-project.sh" ]]; then
        log_error "配置脚本不存在: setup-project.sh"
        exit 1
    fi
    
    # 运行配置脚本
    log_info "运行配置脚本..."
    echo "y" | ./setup-project.sh example-project-config.json
    
    log_success "配置应用完成"
}

# 验证结果
verify_results() {
    log_step "验证配置结果..."
    
    local errors=0
    
    # 检查关键文件是否存在
    local key_files=(
        "backend/pom.xml"
        "backend/web-api/src/main/resources/application.yml"
        "frontend/package.json"
        "frontend/.env.development"
        "backend/sql/init.sql"
    )
    
    for file in "${key_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            log_error "关键文件缺失: $file"
            ((errors++))
        fi
    done
    
    # 检查包目录是否正确重命名
    if [[ -d "backend/web-api/src/main/java/com/ecommerce" ]]; then
        log_success "Java 包目录重命名成功"
    else
        log_error "Java 包目录重命名失败"
        ((errors++))
    fi
    
    # 检查配置文件内容
    if grep -q "ecommerce" "backend/web-api/src/main/resources/application.yml"; then
        log_success "后端配置文件更新成功"
    else
        log_error "后端配置文件更新失败"
        ((errors++))
    fi
    
    if grep -q "ecommerce-admin-frontend" "frontend/package.json"; then
        log_success "前端配置文件更新成功"
    else
        log_error "前端配置文件更新失败"
        ((errors++))
    fi
    
    if [[ $errors -eq 0 ]]; then
        log_success "配置结果验证通过"
        return 0
    else
        log_error "发现 $errors 个验证错误"
        return 1
    fi
}

# 显示下一步操作
show_next_steps() {
    echo ""
    echo "🎉 演示完成！"
    echo "============="
    echo ""
    echo "演示项目已创建在: demo-ecommerce-admin/"
    echo ""
    echo "下一步操作："
    echo ""
    echo "1. 进入项目目录:"
    echo "   cd demo-ecommerce-admin"
    echo ""
    echo "2. 配置数据库:"
    echo "   mysql -u root -p"
    echo "   CREATE DATABASE ecommerce_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    echo "   source backend/sql/init.sql"
    echo ""
    echo "3. 启动后端服务:"
    echo "   cd backend/web-api"
    echo "   mvn spring-boot:run"
    echo ""
    echo "4. 启动前端服务:"
    echo "   cd frontend"
    echo "   npm install"
    echo "   npm run dev"
    echo ""
    echo "5. 访问应用:"
    echo "   前端: http://localhost:3001"
    echo "   后端: http://localhost:8081/api"
    echo "   监控: http://localhost:8081/druid"
    echo ""
    echo "默认登录账号:"
    echo "   用户名: ecommerce_admin"
    echo "   密码: admin2024!"
    echo ""
}

# 清理函数
cleanup() {
    if [[ -d "demo-ecommerce-admin" ]]; then
        log_info "清理演示目录..."
        rm -rf "demo-ecommerce-admin"
    fi
}

# 主函数
main() {
    # 设置错误处理
    trap cleanup EXIT
    
    show_welcome
    
    # 询问是否继续
    read -p "是否继续演示？(y/N): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "演示已取消"
        exit 0
    fi
    
    echo ""
    
    # 执行演示步骤
    check_dependencies
    validate_config
    create_project_copy
    apply_configuration
    
    if verify_results; then
        show_next_steps
        
        # 询问是否保留演示项目
        echo ""
        read -p "是否保留演示项目目录？(y/N): " -n 1 -r
        echo ""
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            # 取消清理
            trap - EXIT
            log_success "演示项目已保留在: demo-ecommerce-admin/"
        else
            log_info "演示项目将被清理"
        fi
    else
        log_error "演示失败，请检查错误信息"
        exit 1
    fi
}

# 执行主函数
main "$@"
