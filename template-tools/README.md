# Template Tools - 模板工具集

本目录包含了 Template Admin 项目模板的所有配置工具和脚本。

## 📁 目录结构

```
template-tools/
├── project-config.json           # 默认配置模板
├── example-project-config.json   # 示例项目配置
├── setup-project.sh             # 项目生成脚本
├── validate-config.sh           # 配置验证脚本
├── quick-start.sh               # 快速演示脚本
├── PROJECT-SETUP-GUIDE.md       # 详细配置指南
├── TEMPLATE-FEATURES.md         # 模板功能说明
└── README.md                    # 本文档
```

## 🚀 快速开始

### ⚠️ 重要前提

**在使用模板工具前，请确保已断开与原始Git仓库的连接：**

```bash
# 1. 克隆模板到新项目目录
git clone <template-repo-url> <new-project-name>
cd <new-project-name>

# 2. 断开与模板仓库的连接
rm -rf .git

# 3. 现在可以安全使用模板工具
```

### 从项目根目录使用（推荐）

```bash
# 快速演示
./create-project.sh demo

# 验证配置文件
./create-project.sh validate template-tools/example-project-config.json

# 创建新项目
./create-project.sh create my-project-config.json
```

### 直接在template-tools目录使用

```bash
cd template-tools

# 快速演示
./quick-start.sh

# 验证配置文件
./validate-config.sh example-project-config.json

# 创建新项目
./setup-project.sh my-project-config.json
```

## 🔧 工具说明

### setup-project.sh
**项目生成脚本** - 根据配置文件生成新项目

**功能:**
- 读取JSON配置文件
- 替换项目名称、包路径、数据库配置等
- 重命名Java包目录结构
- 更新Maven和NPM配置文件
- 同步更新README文档

**使用方法:**
```bash
./setup-project.sh <config-file>
```

### validate-config.sh
**配置验证脚本** - 验证配置文件的正确性

**功能:**
- JSON格式验证
- 必需字段检查
- 项目名称和包名格式验证
- 端口冲突检查
- 安全配置验证

**使用方法:**
```bash
./validate-config.sh <config-file>
```

### quick-start.sh
**快速演示脚本** - 完整演示项目生成流程

**功能:**
- 环境依赖检查
- 配置文件验证
- 创建演示项目
- 验证生成结果

**使用方法:**
```bash
./quick-start.sh
```

## 📝 配置文件

### project-config.json
默认配置模板，包含所有可配置项的默认值。

### example-project-config.json
电商项目示例配置，展示如何配置一个具体的项目。

## 📖 详细文档

- **PROJECT-SETUP-GUIDE.md** - 详细的配置指南和使用说明
- **TEMPLATE-FEATURES.md** - 模板功能完整说明

## ⚠️ 注意事项

1. **Git连接检查**: 所有脚本都会检查Git状态，确保不会污染模板工程
2. **运行位置**: 脚本需要在template-tools目录下运行，或使用根目录的create-project.sh
3. **依赖要求**: 需要安装jq、sed、find等工具
4. **配置验证**: 建议在生成项目前先验证配置文件
5. **断开连接**: 使用前必须先断开与原始Git仓库的连接

### Git安全检查

所有脚本都内置了Git状态检查功能：

- **setup-project.sh**: 检查是否存在 `../.git` 目录
- **quick-start.sh**: 检查是否存在 `../.git` 目录
- **create-project.sh**: 检查是否存在 `.git` 目录

如果检测到Git连接，脚本会：
1. 显示警告信息
2. 提供断开连接的建议
3. 询问是否继续执行（不推荐）
4. 用户可选择取消操作

## 🔗 相关链接

- [项目主页](../README.md)
- [前端文档](../frontend/README.md)
- [后端文档](../backend/README.md)
- [OAuth2配置](../OAuth2-解决方案.md)

---

Template Admin 让项目创建变得简单高效！🚀
