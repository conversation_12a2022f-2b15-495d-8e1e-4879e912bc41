# Template Admin 模板工程功能说明

## 📋 概述

Template Admin 现在是一个完整的可配置项目模板，支持通过配置文件和自动化脚本快速生成新项目。本文档详细说明了模板工程的所有功能和使用方法。

## 🎯 核心功能

### 1. 配置驱动的项目生成
- **配置文件**: `project-config.json` - 包含项目的所有可配置参数
- **生成脚本**: `setup-project.sh` - 一键生成新项目
- **验证脚本**: `validate-config.sh` - 配置文件验证工具
- **演示脚本**: `quick-start.sh` - 快速演示整个流程

### 2. 智能配置替换
自动替换以下内容：
- **项目名称和描述**
- **Java包路径和目录结构**
- **Maven GroupId 和 ArtifactId**
- **数据库名称和连接配置**
- **应用端口和服务配置**
- **安全密钥和认证配置**
- **OAuth2 客户端配置**
- **前端包名和环境变量**
- **README 文档内容**

### 3. 配置验证机制
- **JSON格式验证**
- **必需字段检查**
- **项目名称格式验证**
- **Java包名格式验证**
- **端口号范围和冲突检查**
- **数据库配置验证**
- **安全配置强度检查**

## 📁 文件结构

### 配置文件
```
project-config.json              # 默认配置模板
example-project-config.json     # 示例项目配置
```

### 脚本文件
```
setup-project.sh                 # 项目生成脚本
validate-config.sh              # 配置验证脚本
quick-start.sh                  # 快速演示脚本
```

### 文档文件
```
PROJECT-SETUP-GUIDE.md          # 详细配置指南
TEMPLATE-FEATURES.md            # 模板功能说明（本文档）
README.md                       # 更新的主文档
```

## 🔧 配置项详解

### 项目基本信息
```json
{
  "project": {
    "name": "项目名称（小写，连字符分隔）",
    "displayName": "显示名称",
    "description": "项目描述",
    "version": "版本号",
    "author": "作者",
    "email": "联系邮箱"
  }
}
```

### 后端配置
```json
{
  "backend": {
    "groupId": "Maven GroupId",
    "artifactId": "Maven ArtifactId", 
    "packageName": "Java包名",
    "applicationName": "应用名称",
    "serverPort": "服务端口",
    "contextPath": "上下文路径"
  }
}
```

### 前端配置
```json
{
  "frontend": {
    "name": "NPM包名",
    "title": "页面标题",
    "devPort": "开发端口",
    "apiBaseUrl": "API基础地址"
  }
}
```

### 数据库配置
```json
{
  "database": {
    "name": "数据库名",
    "host": "数据库主机",
    "port": "数据库端口",
    "username": "用户名",
    "password": "密码",
    "charset": "字符集",
    "collation": "排序规则"
  }
}
```

### 安全配置
```json
{
  "security": {
    "jwtSecret": "JWT密钥",
    "accessTokenExpiration": "访问令牌过期时间",
    "refreshTokenExpiration": "刷新令牌过期时间",
    "druidUsername": "Druid监控用户名",
    "druidPassword": "Druid监控密码"
  }
}
```

### OAuth2配置
```json
{
  "oauth2": {
    "google": {
      "clientId": "Google客户端ID",
      "clientSecret": "Google客户端密钥"
    },
    "github": {
      "clientId": "GitHub客户端ID", 
      "clientSecret": "GitHub客户端密钥"
    }
  }
}
```

## 🚀 使用流程

### 1. 快速体验
```bash
# 运行演示脚本
./quick-start.sh
```

### 2. 创建新项目
```bash
# 1. 复制配置文件
cp project-config.json my-project-config.json

# 2. 编辑配置文件
vim my-project-config.json

# 3. 验证配置
./validate-config.sh my-project-config.json

# 4. 生成项目
./setup-project.sh my-project-config.json

# 5. 初始化数据库
mysql -u root -p < backend/sql/init.sql

# 6. 启动项目
cd backend/web-api && mvn spring-boot:run
cd frontend && npm install && npm run dev
```

## 🔍 脚本功能详解

### setup-project.sh
- **依赖检查**: 验证系统依赖（jq、sed、find等）
- **配置读取**: 解析JSON配置文件
- **后端配置**: 更新Maven配置、Java包名、应用配置
- **前端配置**: 更新NPM配置、环境变量
- **包重命名**: 自动重命名Java包目录结构
- **文档更新**: 同步更新README文档

### validate-config.sh
- **JSON验证**: 检查配置文件格式
- **字段验证**: 验证必需字段完整性
- **格式验证**: 验证项目名、包名格式
- **端口验证**: 检查端口范围和冲突
- **安全验证**: 检查密钥强度和安全配置
- **配置摘要**: 显示配置信息摘要

### quick-start.sh
- **环境检查**: 验证系统环境和依赖
- **演示流程**: 完整演示项目生成过程
- **结果验证**: 验证生成项目的正确性
- **清理机制**: 可选的演示项目清理

## 🎨 自定义扩展

### 添加新的配置项
1. 在 `project-config.json` 中添加新字段
2. 在 `setup-project.sh` 中添加处理逻辑
3. 在 `validate-config.sh` 中添加验证规则

### 支持新的文件类型
1. 在 `setup-project.sh` 中添加文件处理逻辑
2. 使用 `replace_in_file` 或 `replace_in_directory` 函数

### 自定义验证规则
1. 在 `validate-config.sh` 中添加新的验证函数
2. 在主函数中调用新的验证函数

## 🔒 安全考虑

### 配置文件安全
- 不要在配置文件中硬编码敏感信息
- 使用环境变量存储OAuth2密钥
- 定期更换JWT密钥

### 脚本安全
- 脚本使用 `set -e` 确保错误时退出
- 输入验证防止注入攻击
- 文件操作前检查路径合法性

## 📈 最佳实践

### 项目命名
- 使用小写字母和连字符
- 避免特殊字符和空格
- 保持名称简洁明了

### 包名规范
- 使用公司域名倒序
- 遵循Java包命名规范
- 避免使用保留关键字

### 密码安全
- 使用强密码（至少8位）
- 包含大小写字母、数字和特殊字符
- 定期更换密码

### 版本管理
- 使用语义化版本号
- 及时更新版本信息
- 维护版本变更日志

## 🤝 贡献指南

欢迎为模板工程贡献代码和建议：

1. **提交Issue** - 报告问题或建议改进
2. **Fork项目** - 创建自己的分支
3. **提交PR** - 提交Pull Request
4. **代码审查** - 参与代码审查过程

## 📞 技术支持

如果在使用过程中遇到问题：

- 📖 查看 [PROJECT-SETUP-GUIDE.md](PROJECT-SETUP-GUIDE.md)
- 🐛 提交 GitHub Issues
- 📧 发送邮件至 <EMAIL>

---

Template Admin 模板工程让项目创建变得简单高效！🚀
