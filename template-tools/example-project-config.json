{"project": {"name": "ecommerce-admin", "displayName": "E-commerce Admin", "description": "电商管理后台系统", "version": "1.0.0", "author": "E-commerce Team", "email": "<EMAIL>"}, "backend": {"groupId": "com.ecommerce", "artifactId": "ecommerce-backend", "packageName": "com.ecommerce", "applicationName": "ecommerce-api", "serverPort": 8081, "contextPath": "/"}, "frontend": {"name": "ecommerce-admin-frontend", "title": "E-commerce Admin Dashboard", "devPort": 3001, "apiBaseUrl": "http://localhost:8081/api"}, "database": {"name": "ecommerce_db", "host": "localhost", "port": 3306, "username": "ecommerce_user", "password": "ecommerce_2024!", "charset": "utf8mb4", "collation": "utf8mb4_unicode_ci"}, "security": {"jwtSecret": "ecommerce-super-secret-jwt-key-2024", "accessTokenExpiration": 7200000, "refreshTokenExpiration": 1209600000, "druidUsername": "ecommerce_admin", "druidPassword": "druid_2024!"}, "oauth2": {"google": {"clientId": "your-ecommerce-google-client-id", "clientSecret": "your-ecommerce-google-client-secret"}, "github": {"clientId": "your-ecommerce-github-client-id", "clientSecret": "your-ecommerce-github-client-secret"}}, "defaultUser": {"username": "ecommerce_admin", "password": "admin2024!", "nickname": "电商管理员", "email": "<EMAIL>"}}