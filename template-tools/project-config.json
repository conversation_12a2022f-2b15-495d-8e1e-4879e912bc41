{"project": {"name": "template-admin", "displayName": "Template Admin", "description": "企业级管理后台系统", "version": "1.0.0", "author": "Template Admin Team", "email": "<EMAIL>"}, "backend": {"groupId": "com.example", "artifactId": "backend-parent", "packageName": "com.example", "applicationName": "backend-api", "serverPort": 8080, "contextPath": "/"}, "frontend": {"name": "any-router-dashboard", "title": "Template Admin", "devPort": 3000, "apiBaseUrl": "http://localhost:8080/api"}, "database": {"name": "ai", "host": "localhost", "port": 3306, "username": "root", "password": "root", "charset": "utf8mb4", "collation": "utf8mb4_unicode_ci"}, "security": {"jwtSecret": "your-jwt-secret-key", "accessTokenExpiration": 3600000, "refreshTokenExpiration": 604800000, "druidUsername": "admin", "druidPassword": "123456"}, "oauth2": {"google": {"clientId": "your-google-client-id", "clientSecret": "your-google-client-secret"}, "github": {"clientId": "your-github-client-id", "clientSecret": "your-github-client-secret"}}, "defaultUser": {"username": "admin", "password": "123456", "nickname": "管理员", "email": "<EMAIL>"}}