#!/bin/bash

# 项目模板配置脚本
# 用于基于模板工程快速创建新项目

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Git状态
check_git_status() {
    log_info "检查Git状态..."

    # 检查是否在Git仓库中
    if [[ -d "../.git" ]]; then
        log_error "检测到项目仍然连接到Git仓库！"
        echo ""
        echo "⚠️  为了避免污染模板工程，请先断开与Git的连接："
        echo ""
        echo "方法一：删除.git目录（推荐）"
        echo "  rm -rf .git"
        echo ""
        echo "方法二：重新初始化Git仓库"
        echo "  rm -rf .git"
        echo "  git init"
        echo "  git remote add origin <your-new-repository-url>"
        echo ""
        echo "方法三：克隆到新目录"
        echo "  git clone <template-repo-url> <new-project-name>"
        echo "  cd <new-project-name>"
        echo "  rm -rf .git"
        echo ""
        read -p "是否继续执行（不推荐）？(y/N): " -n 1 -r
        echo ""
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "操作已取消，请先断开Git连接"
            exit 0
        fi
        log_warning "⚠️  继续执行可能会污染模板工程！"
    else
        log_success "Git状态检查通过（未检测到.git目录）"
    fi
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."

    # 检查 jq
    if ! command -v jq &> /dev/null; then
        log_error "jq 未安装，请先安装 jq"
        echo "Ubuntu/Debian: sudo apt-get install jq"
        echo "CentOS/RHEL: sudo yum install jq"
        echo "macOS: brew install jq"
        exit 1
    fi

    # 检查 sed
    if ! command -v sed &> /dev/null; then
        log_error "sed 未安装"
        exit 1
    fi

    # 检查 find
    if ! command -v find &> /dev/null; then
        log_error "find 未安装"
        exit 1
    fi

    log_success "依赖检查完成"
}

# 读取配置文件
read_config() {
    local config_file="$1"
    
    if [[ ! -f "$config_file" ]]; then
        log_error "配置文件不存在: $config_file"
        exit 1
    fi
    
    # 验证 JSON 格式
    if ! jq empty "$config_file" 2>/dev/null; then
        log_error "配置文件格式错误: $config_file"
        exit 1
    fi
    
    log_success "配置文件读取成功"
}

# 替换文件内容
replace_in_file() {
    local file="$1"
    local search="$2"
    local replace="$3"
    
    if [[ -f "$file" ]]; then
        # 使用 sed 进行替换，兼容 macOS 和 Linux
        if [[ "$OSTYPE" == "darwin"* ]]; then
            sed -i '' "s|$search|$replace|g" "$file"
        else
            sed -i "s|$search|$replace|g" "$file"
        fi
        log_info "已更新文件: $file"
    else
        log_warning "文件不存在: $file"
    fi
}

# 替换目录中的文件内容
replace_in_directory() {
    local dir="$1"
    local search="$2"
    local replace="$3"
    local file_pattern="$4"
    
    if [[ -d "$dir" ]]; then
        find "$dir" -name "$file_pattern" -type f -exec grep -l "$search" {} \; | while read -r file; do
            replace_in_file "$file" "$search" "$replace"
        done
    fi
}

# 重命名 Java 包目录
rename_package_directories() {
    local old_package="$1"
    local new_package="$2"
    local base_dir="$3"
    
    # 将包名转换为路径
    local old_path=$(echo "$old_package" | tr '.' '/')
    local new_path=$(echo "$new_package" | tr '.' '/')
    
    # 查找并重命名包目录
    find "$base_dir" -type d -path "*/$old_path" | while read -r old_dir; do
        local new_dir=$(echo "$old_dir" | sed "s|$old_path|$new_path|")
        local parent_dir=$(dirname "$new_dir")
        
        # 创建新的父目录
        mkdir -p "$parent_dir"
        
        # 移动目录
        if [[ "$old_dir" != "$new_dir" ]]; then
            mv "$old_dir" "$new_dir"
            log_info "重命名包目录: $old_dir -> $new_dir"
        fi
    done
}

# 配置后端项目
configure_backend() {
    local config_file="$1"

    log_info "配置后端项目..."

    # 读取配置
    local group_id=$(jq -r '.backend.groupId' "$config_file")
    local artifact_id=$(jq -r '.backend.artifactId' "$config_file")
    local package_name=$(jq -r '.backend.packageName' "$config_file")
    local app_name=$(jq -r '.backend.applicationName' "$config_file")
    local server_port=$(jq -r '.backend.serverPort' "$config_file")
    local context_path=$(jq -r '.backend.contextPath' "$config_file")
    local version=$(jq -r '.project.version' "$config_file")
    local description=$(jq -r '.project.description' "$config_file")

    # 数据库配置
    local db_name=$(jq -r '.database.name' "$config_file")
    local db_host=$(jq -r '.database.host' "$config_file")
    local db_port=$(jq -r '.database.port' "$config_file")
    local db_username=$(jq -r '.database.username' "$config_file")
    local db_password=$(jq -r '.database.password' "$config_file")

    # 安全配置
    local jwt_secret=$(jq -r '.security.jwtSecret' "$config_file")
    local access_token_exp=$(jq -r '.security.accessTokenExpiration' "$config_file")
    local refresh_token_exp=$(jq -r '.security.refreshTokenExpiration' "$config_file")
    local druid_username=$(jq -r '.security.druidUsername' "$config_file")
    local druid_password=$(jq -r '.security.druidPassword' "$config_file")

    # OAuth2 配置
    local google_client_id=$(jq -r '.oauth2.google.clientId' "$config_file")
    local google_client_secret=$(jq -r '.oauth2.google.clientSecret' "$config_file")
    local github_client_id=$(jq -r '.oauth2.github.clientId' "$config_file")
    local github_client_secret=$(jq -r '.oauth2.github.clientSecret' "$config_file")

    # 更新 pom.xml 文件
    replace_in_file "../backend/pom.xml" "com.example" "$group_id"
    replace_in_file "../backend/pom.xml" "backend-parent" "$artifact_id"
    replace_in_file "../backend/pom.xml" "1.0.0" "$version"
    replace_in_file "../backend/pom.xml" "Backend Multi-Module Project" "$description"

    # 更新子模块 pom.xml
    for module in common dao service web-api; do
        if [[ -f "../backend/$module/pom.xml" ]]; then
            replace_in_file "../backend/$module/pom.xml" "com.example" "$group_id"
            replace_in_file "../backend/$module/pom.xml" "backend-parent" "$artifact_id"
            replace_in_file "../backend/$module/pom.xml" "1.0.0" "$version"
        fi
    done

    # 更新 application.yml
    local app_yml="../backend/web-api/src/main/resources/application.yml"
    replace_in_file "$app_yml" "backend-api" "$app_name"
    replace_in_file "$app_yml" "8080" "$server_port"
    replace_in_file "$app_yml" "******************************" "***************************************"
    replace_in_file "$app_yml" "username: root" "username: $db_username"
    replace_in_file "$app_yml" "password: root" "password: $db_password"
    replace_in_file "$app_yml" "com.example.dao.entity" "$package_name.dao.entity"
    replace_in_file "$app_yml" "com.example: debug" "$package_name: debug"
    replace_in_file "$app_yml" "login-username: admin" "login-username: $druid_username"
    replace_in_file "$app_yml" "login-password: 123456" "login-password: $druid_password"

    # 更新 OAuth2 配置
    replace_in_file "$app_yml" "your-google-client-id" "$google_client_id"
    replace_in_file "$app_yml" "your-google-client-secret" "$google_client_secret"
    replace_in_file "$app_yml" "your-github-client-id" "$github_client_id"
    replace_in_file "$app_yml" "your-github-client-secret" "$github_client_secret"

    # 更新 application-oauth2.yml 文件
    local oauth2_yml="../backend/web-api/src/main/resources/application-oauth2.yml"
    if [[ -f "$oauth2_yml" ]]; then
        # 创建临时文件进行替换
        local temp_file="${oauth2_yml}.tmp"

        # 使用awk进行精确替换
        awk -v google_id="$google_client_id" -v google_secret="$google_client_secret" \
            -v github_id="$github_client_id" -v github_secret="$github_client_secret" '
        /^          google:/ { in_google = 1; in_github = 0 }
        /^          github:/ { in_google = 0; in_github = 1 }
        /^        provider:/ { in_google = 0; in_github = 0 }

        in_google && /client-id:/ {
            gsub(/client-id: "[^"]*"/, "client-id: \"" google_id "\"")
        }
        in_google && /client-secret:/ {
            gsub(/client-secret: "[^"]*"/, "client-secret: \"" google_secret "\"")
        }
        in_github && /client-id:/ {
            gsub(/client-id: "[^"]*"/, "client-id: \"" github_id "\"")
        }
        in_github && /client-secret:/ {
            gsub(/client-secret: "[^"]*"/, "client-secret: \"" github_secret "\"")
        }
        { print }
        ' "$oauth2_yml" > "$temp_file" && mv "$temp_file" "$oauth2_yml"

        log_info "已更新 application-oauth2.yml 配置"
    fi

    # 重命名包目录
    if [[ "$package_name" != "com.example" ]]; then
        rename_package_directories "com.example" "$package_name" "../backend"
    fi

    # 更新 Java 文件中的包名
    replace_in_directory "../backend" "package com.example" "package $package_name" "*.java"
    replace_in_directory "../backend" "import com.example" "import $package_name" "*.java"

    # 更新 Java 文件中的完全限定类名引用
    replace_in_directory "../backend" "com.example.dao.entity" "$package_name.dao.entity" "*.java"
    replace_in_directory "../backend" "com.example.service" "$package_name.service" "*.java"
    replace_in_directory "../backend" "com.example.common" "$package_name.common" "*.java"
    replace_in_directory "../backend" "com.example.controller" "$package_name.controller" "*.java"
    replace_in_directory "../backend" "com.example.config" "$package_name.config" "*.java"
    replace_in_directory "../backend" "com.example.security" "$package_name.security" "*.java"

    # 更新数据库初始化脚本
    replace_in_file "../backend/sql/init.sql" "CREATE DATABASE IF NOT EXISTS ai" "CREATE DATABASE IF NOT EXISTS $db_name"
    replace_in_file "../backend/sql/init.sql" "USE ai;" "USE $db_name;"

    log_success "后端项目配置完成"
}

# 配置前端项目
configure_frontend() {
    local config_file="$1"

    log_info "配置前端项目..."

    # 读取配置
    local name=$(jq -r '.frontend.name' "$config_file")
    local title=$(jq -r '.frontend.title' "$config_file")
    local version=$(jq -r '.project.version' "$config_file")
    local description=$(jq -r '.project.description' "$config_file")
    local dev_port=$(jq -r '.frontend.devPort' "$config_file")
    local api_base_url=$(jq -r '.frontend.apiBaseUrl' "$config_file")

    # 更新 package.json
    local package_json="../frontend/package.json"
    if [[ -f "$package_json" ]]; then
        # 使用 jq 更新 JSON 文件
        jq --arg name "$name" \
           --arg version "$version" \
           '.name = $name | .version = $version' \
           "$package_json" > "${package_json}.tmp" && mv "${package_json}.tmp" "$package_json"
        log_info "已更新 package.json"
    fi

    # 更新 package-lock.json
    local package_lock="../frontend/package-lock.json"
    if [[ -f "$package_lock" ]]; then
        jq --arg name "$name" \
           --arg version "$version" \
           '.name = $name | .version = $version | .packages."".name = $name | .packages."".version = $version' \
           "$package_lock" > "${package_lock}.tmp" && mv "${package_lock}.tmp" "$package_lock"
        log_info "已更新 package-lock.json"
    fi

    # 更新 index.html 标题
    replace_in_file "../frontend/index.html" "<title>.*</title>" "<title>$title</title>"

    # 创建环境变量文件
    cat > "../frontend/.env.development" << EOF
VITE_API_BASE_URL=$api_base_url
VITE_APP_TITLE=$title (Dev)
EOF

    cat > "../frontend/.env.production" << EOF
VITE_API_BASE_URL=$api_base_url
VITE_APP_TITLE=$title
EOF

    log_success "前端项目配置完成"
}

# 更新 README 文档
update_readme() {
    local config_file="$1"

    log_info "更新 README 文档..."

    local project_name=$(jq -r '.project.name' "$config_file")
    local display_name=$(jq -r '.project.displayName' "$config_file")
    local description=$(jq -r '.project.description' "$config_file")
    local db_name=$(jq -r '.database.name' "$config_file")
    local frontend_port=$(jq -r '.frontend.devPort' "$config_file")
    local backend_port=$(jq -r '.backend.serverPort' "$config_file")

    # 更新主 README
    replace_in_file "../README.md" "template-admin" "$project_name"
    replace_in_file "../README.md" "Template Admin" "$display_name"
    replace_in_file "../README.md" "企业级管理后台系统" "$description"
    replace_in_file "../README.md" "CREATE DATABASE ai" "CREATE DATABASE $db_name"
    replace_in_file "../README.md" "mysql -u root -p ai" "mysql -u root -p $db_name"
    replace_in_file "../README.md" "http://localhost:3000" "http://localhost:$frontend_port"
    replace_in_file "../README.md" "http://localhost:8080" "http://localhost:$backend_port"

    # 更新前端 README
    replace_in_file "../frontend/README.md" "Template Admin" "$display_name"
    replace_in_file "../frontend/README.md" "any-router-dashboard" "$project_name-frontend"
    replace_in_file "../frontend/README.md" "http://localhost:3000" "http://localhost:$frontend_port"

    # 更新后端 README
    replace_in_file "../backend/README.md" "Template Admin" "$display_name"
    replace_in_file "../backend/README.md" "CREATE DATABASE ai" "CREATE DATABASE $db_name"
    replace_in_file "../backend/README.md" "mysql -u root -p ai" "mysql -u root -p $db_name"
    replace_in_file "../backend/README.md" "http://localhost:8080" "http://localhost:$backend_port"

    log_success "README 文档更新完成"
}

# 主函数
main() {
    echo "🚀 项目模板配置工具"
    echo "===================="
    echo ""
    
    # 检查参数
    if [[ $# -eq 0 ]]; then
        echo "使用方法: $0 <config-file>"
        echo "示例: $0 project-config.json"
        exit 1
    fi
    
    local config_file="$1"
    
    # 检查依赖
    check_dependencies
    
    # 读取配置
    read_config "$config_file"
    
    # 显示配置信息
    local project_name=$(jq -r '.project.name' "$config_file")
    local display_name=$(jq -r '.project.displayName' "$config_file")
    echo "项目名称: $project_name"
    echo "显示名称: $display_name"
    echo ""
    
    # 确认执行
    read -p "确认要使用此配置创建项目吗？(y/N): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        exit 0
    fi
    
    # 检查Git状态
    check_git_status

    # 执行配置
    configure_backend "$config_file"
    configure_frontend "$config_file"
    update_readme "$config_file"
    
    echo ""
    log_success "🎉 项目配置完成！"
    echo ""
    echo "下一步操作："
    echo "1. 配置数据库: mysql -u root -p < ../backend/sql/init.sql"
    echo "2. 启动后端: cd ../backend/web-api && mvn spring-boot:run"
    echo "3. 启动前端: cd ../frontend && npm install && npm run dev"
    echo ""
    echo "访问地址："
    echo "- 前端: http://localhost:$(jq -r '.frontend.devPort' "$config_file")"
    echo "- 后端: http://localhost:$(jq -r '.backend.serverPort' "$config_file")/api"
}

# 执行主函数
main "$@"
