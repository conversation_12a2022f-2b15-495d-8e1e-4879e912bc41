#!/bin/bash

# 配置文件验证脚本
# 用于验证项目配置文件的正确性

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 验证 JSON 格式
validate_json() {
    local config_file="$1"
    
    if [[ ! -f "$config_file" ]]; then
        log_error "配置文件不存在: $config_file"
        return 1
    fi
    
    if ! jq empty "$config_file" 2>/dev/null; then
        log_error "配置文件格式错误: $config_file"
        return 1
    fi
    
    log_success "JSON 格式验证通过"
    return 0
}

# 验证必需字段
validate_required_fields() {
    local config_file="$1"
    local errors=0
    
    # 必需字段列表
    local required_fields=(
        ".project.name"
        ".project.displayName"
        ".project.description"
        ".project.version"
        ".backend.groupId"
        ".backend.packageName"
        ".backend.serverPort"
        ".frontend.name"
        ".frontend.title"
        ".frontend.devPort"
        ".database.name"
        ".database.username"
        ".database.password"
    )
    
    log_info "验证必需字段..."
    
    for field in "${required_fields[@]}"; do
        local value=$(jq -r "$field" "$config_file" 2>/dev/null)
        if [[ "$value" == "null" || "$value" == "" ]]; then
            log_error "缺少必需字段: $field"
            ((errors++))
        fi
    done
    
    if [[ $errors -eq 0 ]]; then
        log_success "必需字段验证通过"
        return 0
    else
        log_error "发现 $errors 个字段错误"
        return 1
    fi
}

# 验证项目名称格式
validate_project_name() {
    local config_file="$1"
    local project_name=$(jq -r '.project.name' "$config_file")
    
    log_info "验证项目名称格式..."
    
    # 检查项目名称格式（小写字母、数字、连字符）
    if [[ ! "$project_name" =~ ^[a-z0-9-]+$ ]]; then
        log_error "项目名称格式错误: $project_name"
        log_error "项目名称只能包含小写字母、数字和连字符"
        return 1
    fi
    
    # 检查是否以连字符开头或结尾
    if [[ "$project_name" =~ ^- || "$project_name" =~ -$ ]]; then
        log_error "项目名称不能以连字符开头或结尾: $project_name"
        return 1
    fi
    
    log_success "项目名称格式验证通过: $project_name"
    return 0
}

# 验证包名格式
validate_package_name() {
    local config_file="$1"
    local package_name=$(jq -r '.backend.packageName' "$config_file")
    
    log_info "验证包名格式..."
    
    # 检查包名格式
    if [[ ! "$package_name" =~ ^[a-z][a-z0-9]*(\.[a-z][a-z0-9]*)*$ ]]; then
        log_error "包名格式错误: $package_name"
        log_error "包名应该遵循 Java 包命名规范，如: com.example"
        return 1
    fi
    
    log_success "包名格式验证通过: $package_name"
    return 0
}

# 验证端口号
validate_ports() {
    local config_file="$1"
    local backend_port=$(jq -r '.backend.serverPort' "$config_file")
    local frontend_port=$(jq -r '.frontend.devPort' "$config_file")
    local db_port=$(jq -r '.database.port' "$config_file")
    
    log_info "验证端口号..."
    
    # 验证端口范围
    for port in "$backend_port" "$frontend_port" "$db_port"; do
        if [[ ! "$port" =~ ^[0-9]+$ ]] || [[ "$port" -lt 1 ]] || [[ "$port" -gt 65535 ]]; then
            log_error "端口号无效: $port"
            return 1
        fi
    done
    
    # 检查端口冲突
    if [[ "$backend_port" == "$frontend_port" ]]; then
        log_error "后端端口和前端端口冲突: $backend_port"
        return 1
    fi
    
    log_success "端口号验证通过"
    return 0
}

# 验证数据库配置
validate_database() {
    local config_file="$1"
    local db_name=$(jq -r '.database.name' "$config_file")
    local db_username=$(jq -r '.database.username' "$config_file")
    local db_password=$(jq -r '.database.password' "$config_file")
    
    log_info "验证数据库配置..."
    
    # 验证数据库名格式
    if [[ ! "$db_name" =~ ^[a-zA-Z][a-zA-Z0-9_]*$ ]]; then
        log_error "数据库名格式错误: $db_name"
        log_error "数据库名应该以字母开头，只包含字母、数字和下划线"
        return 1
    fi
    
    # 检查密码强度
    if [[ ${#db_password} -lt 6 ]]; then
        log_warning "数据库密码过短，建议使用至少6位密码"
    fi
    
    if [[ "$db_password" == "root" || "$db_password" == "123456" ]]; then
        log_warning "使用了常见的弱密码，建议更换为强密码"
    fi
    
    log_success "数据库配置验证通过"
    return 0
}

# 验证安全配置
validate_security() {
    local config_file="$1"
    local jwt_secret=$(jq -r '.security.jwtSecret' "$config_file")
    local access_exp=$(jq -r '.security.accessTokenExpiration' "$config_file")
    local refresh_exp=$(jq -r '.security.refreshTokenExpiration' "$config_file")
    
    log_info "验证安全配置..."
    
    # 验证 JWT 密钥长度
    if [[ ${#jwt_secret} -lt 32 ]]; then
        log_warning "JWT 密钥过短，建议使用至少32位的强密钥"
    fi
    
    # 检查是否使用默认密钥
    if [[ "$jwt_secret" == "your-jwt-secret-key" ]]; then
        log_error "使用了默认的 JWT 密钥，必须更换为自定义密钥"
        return 1
    fi
    
    # 验证令牌过期时间
    if [[ ! "$access_exp" =~ ^[0-9]+$ ]] || [[ ! "$refresh_exp" =~ ^[0-9]+$ ]]; then
        log_error "令牌过期时间必须是数字"
        return 1
    fi
    
    if [[ "$access_exp" -ge "$refresh_exp" ]]; then
        log_error "访问令牌过期时间不能大于等于刷新令牌过期时间"
        return 1
    fi
    
    log_success "安全配置验证通过"
    return 0
}

# 显示配置摘要
show_config_summary() {
    local config_file="$1"
    
    echo ""
    echo "📋 配置摘要"
    echo "============"
    echo "项目名称: $(jq -r '.project.name' "$config_file")"
    echo "显示名称: $(jq -r '.project.displayName' "$config_file")"
    echo "项目描述: $(jq -r '.project.description' "$config_file")"
    echo "版本号: $(jq -r '.project.version' "$config_file")"
    echo ""
    echo "后端配置:"
    echo "  包名: $(jq -r '.backend.packageName' "$config_file")"
    echo "  端口: $(jq -r '.backend.serverPort' "$config_file")"
    echo ""
    echo "前端配置:"
    echo "  名称: $(jq -r '.frontend.name' "$config_file")"
    echo "  端口: $(jq -r '.frontend.devPort' "$config_file")"
    echo ""
    echo "数据库配置:"
    echo "  数据库名: $(jq -r '.database.name' "$config_file")"
    echo "  用户名: $(jq -r '.database.username' "$config_file")"
    echo "  主机: $(jq -r '.database.host' "$config_file"):$(jq -r '.database.port' "$config_file")"
    echo ""
}

# 主函数
main() {
    echo "🔍 项目配置验证工具"
    echo "==================="
    echo ""
    
    # 检查参数
    if [[ $# -eq 0 ]]; then
        echo "使用方法: $0 <config-file>"
        echo "示例: $0 project-config.json"
        exit 1
    fi
    
    local config_file="$1"
    local errors=0
    
    # 执行验证
    validate_json "$config_file" || ((errors++))
    validate_required_fields "$config_file" || ((errors++))
    validate_project_name "$config_file" || ((errors++))
    validate_package_name "$config_file" || ((errors++))
    validate_ports "$config_file" || ((errors++))
    validate_database "$config_file" || ((errors++))
    validate_security "$config_file" || ((errors++))
    
    # 显示结果
    echo ""
    if [[ $errors -eq 0 ]]; then
        log_success "✅ 配置验证通过！"
        show_config_summary "$config_file"
        echo ""
        echo "可以运行以下命令创建项目："
        if [[ -f "../create-project.sh" ]]; then
            echo "../create-project.sh create $config_file"
        else
            echo "./setup-project.sh $config_file"
        fi
    else
        log_error "❌ 配置验证失败，发现 $errors 个错误"
        echo "请修复上述错误后重新验证"
        exit 1
    fi
}

# 执行主函数
main "$@"
